"""
Equation Generator for Circuit Analysis
Generates mathematical equations for capacitor currents and voltages
"""

from typing import Dict, <PERSON>, Tuple
from circuit_parser import CircuitPars<PERSON>, CircuitComponent, ComponentType
from dataclasses import dataclass

@dataclass
class CapacitorEquation:
    """Represents equations for a capacitor"""
    name: str
    capacitance: str
    node1: int
    node2: int
    voltage_equation: str
    current_equation: str
    current_equation_discrete: str
    voltage_integration: str

class EquationGenerator:
    """Generates mathematical equations for circuit analysis"""

    def __init__(self, parser: CircuitParser, input_filename: str = ""):
        self.parser = parser
        self.input_filename = input_filename
        self.capacitor_equations: Dict[str, CapacitorEquation] = {}
    
    def generate_capacitor_equations(self) -> Dict[str, CapacitorEquation]:
        """Generate equations for all capacitors in the circuit"""
        capacitors = self.parser.get_capacitors()
        
        for cap_name, cap_component in capacitors.items():
            equation = self._generate_single_capacitor_equation(cap_component)
            self.capacitor_equations[cap_name] = equation
        
        return self.capacitor_equations
    
    def _generate_single_capacitor_equation(self, capacitor: CircuitComponent) -> CapacitorEquation:
        """Generate equations for a single capacitor"""
        cap_name = capacitor.name
        capacitance = capacitor.value
        node1 = capacitor.node1
        node2 = capacitor.node2
        
        # Generate voltage names
        v_node1 = self._get_voltage_name(node1)
        v_node2 = self._get_voltage_name(node2)
        v_cap = f"V_{cap_name.lower()}"
        
        # Voltage equation: V_cap = V_node1 - V_node2
        voltage_equation = f"{v_cap} = {v_node1} - {v_node2}"
        
        # Current equation: I_cap = C * d(V_cap)/dt
        current_equation = f"I_{cap_name.lower()} = {capacitance} * d({v_cap})/dt"
        
        # Discrete current equation: I_cap = C * (V_cap - V_cap_old) / dt
        current_equation_discrete = (
            f"I_{cap_name.lower()} = {capacitance} * "
            f"(({v_node1} - {v_node2}) - {v_cap}_old) / dt"
        )
        
        # Voltage integration equation: V_cap = (1/C) * ∫I_cap dt
        voltage_integration = f"{v_cap} = (1/{capacitance}) * ∫I_{cap_name.lower()} dt"
        
        return CapacitorEquation(
            name=cap_name,
            capacitance=capacitance,
            node1=node1,
            node2=node2,
            voltage_equation=voltage_equation,
            current_equation=current_equation,
            current_equation_discrete=current_equation_discrete,
            voltage_integration=voltage_integration
        )
    
    def _get_voltage_name(self, node_id: int) -> str:
        """Get voltage name for a node"""
        if node_id == 0:
            return "0"  # Ground reference
        elif node_id == 1:
            return "Vu1"
        elif node_id == 2:
            return "Vv1"
        elif node_id == 3:
            return "Vw1"
        else:
            return f"Vn{node_id}"
    
    def generate_latex_equations(self, input_filename: str = "") -> str:
        """Generate LaTeX formatted equations"""
        latex_output = []
        latex_output.append("\\documentclass{article}")
        latex_output.append("\\usepackage{amsmath}")
        latex_output.append("\\begin{document}")
        latex_output.append("\\title{Circuit Equations}")
        if input_filename:
            latex_output.append(f"\\author{{Source file: {input_filename}}}")
        latex_output.append("\\maketitle")
        
        for cap_name, eq in self.capacitor_equations.items():
            latex_output.append(f"\\section{{Capacitor {cap_name}}}")
            latex_output.append("\\begin{align}")
            
            # Voltage equation
            voltage_eq_latex = eq.voltage_equation.replace("_", "_{").replace("V_", "V_{") + "}"
            latex_output.append(f"{voltage_eq_latex} \\\\")
            
            # Current equation
            current_eq_latex = eq.current_equation.replace("_", "_{").replace("I_", "I_{").replace("d(", "\\frac{d(").replace(")/dt", ")}{dt}") + "}"
            latex_output.append(f"{current_eq_latex} \\\\")
            
            # Discrete current equation
            discrete_eq_latex = eq.current_equation_discrete.replace("_", "_{").replace("I_", "I_{") + "}"
            latex_output.append(f"{discrete_eq_latex}")
            
            latex_output.append("\\end{align}")
        
        latex_output.append("\\end{document}")
        return "\n".join(latex_output)
    
    def generate_text_equations(self) -> str:
        """Generate plain text formatted equations"""
        text_output = []
        text_output.append("=== CIRCUIT EQUATIONS ===")
        if self.input_filename:
            text_output.append(f"Source file: {self.input_filename}")
        text_output.append("")

        for cap_name, eq in self.capacitor_equations.items():
            text_output.append(f"Capacitor {cap_name} (C = {eq.capacitance}):")
            text_output.append(f"  Nodes: {eq.node1} - {eq.node2}")
            text_output.append(f"  Voltage: {eq.voltage_equation}")
            text_output.append(f"  Current (continuous): {eq.current_equation}")
            text_output.append(f"  Current (discrete): {eq.current_equation_discrete}")
            text_output.append(f"  Voltage integration: {eq.voltage_integration}")
            text_output.append("")

        return "\n".join(text_output)
    
    def generate_python_code(self) -> str:
        """Generate Python code for numerical computation"""
        code_lines = []
        code_lines.append("# Generated Python code for circuit simulation")
        code_lines.append("import numpy as np")
        code_lines.append("")
        code_lines.append("def calculate_capacitor_currents(Vu1, Vv1, Vw1, Vn1, Cu1, Cv1, Cw1, Vcu1_old, Vcv1_old, Vcw1_old, dt):")
        code_lines.append('    """')
        code_lines.append('    Calculate capacitor currents using discrete equations')
        code_lines.append('    ')
        code_lines.append('    Parameters:')
        code_lines.append('    Vu1, Vv1, Vw1: Input voltages')
        code_lines.append('    Vn1: Common node voltage (usually 0 for ground)')
        code_lines.append('    Cu1, Cv1, Cw1: Capacitance values')
        code_lines.append('    Vcu1_old, Vcv1_old, Vcw1_old: Previous capacitor voltages')
        code_lines.append('    dt: Time step')
        code_lines.append('    """')

        for cap_name, eq in self.capacitor_equations.items():
            cap_lower = cap_name.lower()
            node1_voltage = self._get_voltage_name(eq.node1)
            node2_voltage = self._get_voltage_name(eq.node2)

            # Replace voltage names with actual variables
            if node1_voltage == "0":
                node1_voltage = "0"
            if node2_voltage == "0":
                node2_voltage = "0"
            # Replace Vn4 with Vn1 (common node)
            if node2_voltage == "Vn4":
                node2_voltage = "Vn1"

            code_lines.append(f"    # {cap_name}: I = C * ((V1 - V2) - V_old) / dt")
            code_lines.append(f"    I_{cap_lower} = {eq.capacitance} * (({node1_voltage} - {node2_voltage}) - V{cap_lower}_old) / dt")

        code_lines.append("")
        code_lines.append("    return {")
        for cap_name, eq in self.capacitor_equations.items():
            cap_lower = cap_name.lower()
            code_lines.append(f"        'I_{cap_lower}': I_{cap_lower},")
        code_lines.append("    }")
        code_lines.append("")
        code_lines.append("")
        code_lines.append("# Example usage:")
        code_lines.append("if __name__ == '__main__':")
        code_lines.append("    # Example parameters")
        code_lines.append("    Vu1, Vv1, Vw1 = 1.0, 0.5, -0.5  # Input voltages")
        code_lines.append("    Vn1 = 0.0  # Common node (ground)")
        code_lines.append("    Cu1, Cv1, Cw1 = 1e-6, 1e-6, 1e-6  # Capacitance values (1μF)")
        code_lines.append("    Vcu1_old, Vcv1_old, Vcw1_old = 0.0, 0.0, 0.0  # Previous voltages")
        code_lines.append("    dt = 1e-5  # Time step (10μs)")
        code_lines.append("    ")
        code_lines.append("    currents = calculate_capacitor_currents(")
        code_lines.append("        Vu1, Vv1, Vw1, Vn1, Cu1, Cv1, Cw1,")
        code_lines.append("        Vcu1_old, Vcv1_old, Vcw1_old, dt")
        code_lines.append("    )")
        code_lines.append("    ")
        code_lines.append("    print('Calculated currents:')")
        code_lines.append("    for name, current in currents.items():")
        code_lines.append("        print(f'{name}: {current:.6e} A')")

        return "\n".join(code_lines)

if __name__ == "__main__":
    # Test the equation generator
    from circuit_parser import CircuitParser
    
    parser = CircuitParser()
    parser.parse_file("input/ACF1.cct")
    
    generator = EquationGenerator(parser)
    equations = generator.generate_capacitor_equations()
    
    print(generator.generate_text_equations())
    print("\n" + "="*50 + "\n")
    print(generator.generate_python_code())
