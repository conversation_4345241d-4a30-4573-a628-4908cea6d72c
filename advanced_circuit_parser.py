"""
Advanced Circuit Parser for Complex Circuit Analysis
Supports R, L, C components and generates comprehensive equations
"""

import re
import numpy as np
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
from enum import Enum

class ComponentType(Enum):
    CAPACITOR = "C"
    RESISTOR = "R"
    INDUCTOR = "L"
    VOLTAGE_SOURCE = "V"
    CURRENT_SOURCE = "I"

@dataclass
class CircuitComponent:
    """Represents a circuit component"""
    component_type: ComponentType
    name: str
    node1: int
    node2: int
    value: str
    additional_params: List[str]

@dataclass
class CircuitNode:
    """Represents a circuit node"""
    node_id: int
    voltage_name: str
    connected_components: List[str]
    is_ground: bool = False

class AdvancedCircuitParser:
    """Advanced parser for complex circuit analysis"""
    
    def __init__(self):
        self.components: Dict[str, CircuitComponent] = {}
        self.nodes: Dict[int, CircuitNode] = {}
        self.time_params: Optional[List[str]] = None
        self.version: Optional[str] = None
        self.ground_node: int = 0
    
    def parse_file(self, filepath: str) -> None:
        """Parse a .cct file and extract circuit information"""
        with open(filepath, 'r') as file:
            lines = file.readlines()
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if line.startswith('.TIME'):
                self._parse_time_directive(line)
            elif line.startswith('.TI_DMC_VERSION'):
                self._parse_version_directive(line)
            else:
                self._parse_component_line(line)
        
        self._build_node_connections()
        self._identify_ground_node()
    
    def _parse_time_directive(self, line: str) -> None:
        """Parse .TIME directive"""
        parts = line.split()
        self.time_params = parts[1:]
    
    def _parse_version_directive(self, line: str) -> None:
        """Parse .TI_DMC_VERSION directive"""
        parts = line.split()
        if len(parts) >= 2:
            self.version = parts[1]
    
    def _parse_component_line(self, line: str) -> None:
        """Parse a component line (C, R, L, V, I, etc.)"""
        parts = line.split()
        if len(parts) < 4:
            return
        
        component_type_str = parts[0]
        component_name = parts[1]
        node1 = int(parts[2])
        node2 = int(parts[3])
        
        # Try to map component type
        try:
            component_type = ComponentType(component_type_str)
        except ValueError:
            # Unknown component type, skip for now
            return
        
        # Extract value and additional parameters
        value = parts[4] if len(parts) > 4 else ""
        additional_params = parts[5:] if len(parts) > 5 else []
        
        component = CircuitComponent(
            component_type=component_type,
            name=component_name,
            node1=node1,
            node2=node2,
            value=value,
            additional_params=additional_params
        )
        
        self.components[component_name] = component
    
    def _build_node_connections(self) -> None:
        """Build node connection information"""
        # Initialize nodes
        all_nodes = set()
        for comp in self.components.values():
            all_nodes.add(comp.node1)
            all_nodes.add(comp.node2)
        
        for node_id in all_nodes:
            voltage_name = self._generate_voltage_name(node_id)
            self.nodes[node_id] = CircuitNode(
                node_id=node_id,
                voltage_name=voltage_name,
                connected_components=[],
                is_ground=(node_id == 0)
            )
        
        # Connect components to nodes
        for comp_name, comp in self.components.items():
            self.nodes[comp.node1].connected_components.append(comp_name)
            self.nodes[comp.node2].connected_components.append(comp_name)
    
    def _identify_ground_node(self) -> None:
        """Identify the ground node (usually the most connected node)"""
        if 0 in self.nodes:
            self.ground_node = 0
            self.nodes[0].is_ground = True
        else:
            # Find the node with most connections
            max_connections = 0
            ground_candidate = None
            for node_id, node in self.nodes.items():
                if len(node.connected_components) > max_connections:
                    max_connections = len(node.connected_components)
                    ground_candidate = node_id
            
            if ground_candidate is not None:
                self.ground_node = ground_candidate
                self.nodes[ground_candidate].is_ground = True
    
    def _generate_voltage_name(self, node_id: int) -> str:
        """Generate voltage name for a node"""
        if node_id == 0:
            return "GND"
        else:
            return f"Vn{node_id}"
    
    def get_components_by_type(self, component_type: ComponentType) -> Dict[str, CircuitComponent]:
        """Get all components of a specific type"""
        return {name: comp for name, comp in self.components.items() 
                if comp.component_type == component_type}
    
    def get_capacitors(self) -> Dict[str, CircuitComponent]:
        """Get all capacitor components"""
        return self.get_components_by_type(ComponentType.CAPACITOR)
    
    def get_inductors(self) -> Dict[str, CircuitComponent]:
        """Get all inductor components"""
        return self.get_components_by_type(ComponentType.INDUCTOR)
    
    def get_resistors(self) -> Dict[str, CircuitComponent]:
        """Get all resistor components"""
        return self.get_components_by_type(ComponentType.RESISTOR)
    
    def get_component_by_name(self, name: str) -> Optional[CircuitComponent]:
        """Get component by name"""
        return self.components.get(name)
    
    def get_connected_components(self, node_id: int, component_type: Optional[ComponentType] = None) -> List[CircuitComponent]:
        """Get components connected to a specific node"""
        if node_id not in self.nodes:
            return []
        
        connected = []
        for comp_name in self.nodes[node_id].connected_components:
            comp = self.components[comp_name]
            if component_type is None or comp.component_type == component_type:
                connected.append(comp)
        
        return connected
    
    def find_loops(self) -> List[List[str]]:
        """Find loops in the circuit for KVL analysis"""
        # Simplified loop detection - can be enhanced
        loops = []
        # This is a placeholder for more sophisticated loop detection
        return loops
    
    def print_circuit_info(self) -> None:
        """Print parsed circuit information"""
        print("=== Advanced Circuit Information ===")
        print(f"Version: {self.version}")
        print(f"Time parameters: {self.time_params}")
        print(f"Ground node: {self.ground_node}")
        
        print("\nComponents by type:")
        for comp_type in ComponentType:
            components = self.get_components_by_type(comp_type)
            if components:
                print(f"  {comp_type.value} ({len(components)}):")
                for name, comp in components.items():
                    print(f"    {name}: {comp.node1}-{comp.node2}, value={comp.value}")
        
        print("\nNodes:")
        for node_id, node in self.nodes.items():
            ground_str = " (GROUND)" if node.is_ground else ""
            print(f"  Node {node_id} ({node.voltage_name}){ground_str}: {len(node.connected_components)} connections")

if __name__ == "__main__":
    # Test the advanced parser
    parser = AdvancedCircuitParser()
    parser.parse_file("input/ACF2.cct")
    parser.print_circuit_info()
