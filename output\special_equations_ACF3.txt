=== SPECIAL CIRCUIT ANALYSIS ===
Source file: input/ACF3.cct

Extended <PERSON>'s Voltage Law (KVL) Analysis:
For each independent loop in the circuit:

Loop 1: Cu3 → Cv3 → Cw3
  KVL: V_cu3 + V_cv3 + V_cw3 = 0

Loop Current Analysis:
Independent current relationships:


Energy Analysis:
Total system energy:

Electric energy (capacitors):
  E_cu3 = (1/2) * Cu1 * V_cu3²
  E_cv3 = (1/2) * Cv1 * V_cv3²
  E_cw3 = (1/2) * Cw1 * V_cw3²
  Total electric energy: E_C = E_cu3 + E_cv3 + E_cw3

Total system energy: E_total = E_C

System Matrix Analysis:

State vector:
x = [V_cu3, V_cv3, V_cw3]ᵀ

State-space representation:
dx/dt = A*x + B*u
y = C*x + D*u

Where:
  x = state vector (inductor currents, capacitor voltages)
  u = input vector (voltage sources, current sources)
  y = output vector (measured variables)
  A = system matrix (circuit topology dependent)
  B = input matrix
  C = output matrix
  D = feedthrough matrix
