=== CIRCUIT EQUATIONS ===
Source file: input/ACF3.cct

Capacitor Cu3 (C = Cu1):
  Nodes: 1 - 3
  Voltage: V_cu3 = Vu1 - Vw1
  Current (continuous): I_cu3 = Cu1 * d(V_cu3)/dt
  Current (discrete): I_cu3 = Cu1 * ((Vu1 - Vw1) - V_cu3_old) / dt
  Voltage integration: V_cu3 = (1/Cu1) * ∫I_cu3 dt

Capacitor Cv3 (C = Cv1):
  Nodes: 2 - 3
  Voltage: V_cv3 = Vv1 - Vw1
  Current (continuous): I_cv3 = Cv1 * d(V_cv3)/dt
  Current (discrete): I_cv3 = Cv1 * ((Vv1 - Vw1) - V_cv3_old) / dt
  Voltage integration: V_cv3 = (1/Cv1) * ∫I_cv3 dt

Capacitor Cw3 (C = Cw1):
  Nodes: 4 - 3
  Voltage: V_cw3 = Vn4 - Vw1
  Current (continuous): I_cw3 = Cw1 * d(V_cw3)/dt
  Current (discrete): I_cw3 = Cw1 * ((Vn4 - Vw1) - V_cw3_old) / dt
  Voltage integration: V_cw3 = (1/Cw1) * ∫I_cw3 dt
