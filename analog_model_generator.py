"""
Analog Model Generator
Generates visual analog models for circuit simulation
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Circle
import numpy as np
from typing import Dict, List, Tuple
from circuit_parser import CircuitParser, CircuitComponent
from equation_generator import EquationGenerator

class AnalogModelGenerator:
    """Generates analog model diagrams for circuit simulation"""
    
    def __init__(self, parser: CircuitParser, equation_generator: EquationGenerator):
        self.parser = parser
        self.equation_generator = equation_generator
        self.fig = None
        self.ax = None
    
    def generate_analog_model(self, output_file: str = "analog_model.png") -> None:
        """Generate analog model diagram"""
        capacitors = self.parser.get_capacitors()
        
        # Create figure
        self.fig, self.ax = plt.subplots(1, 1, figsize=(16, 12))
        self.ax.set_xlim(0, 20)
        self.ax.set_ylim(0, 15)
        self.ax.set_aspect('equal')
        self.ax.axis('off')
        
        # Generate model for each capacitor
        y_positions = [12, 8, 4]  # Vertical positions for Cu1, Cv1, Cw1
        
        for i, (cap_name, cap_component) in enumerate(capacitors.items()):
            if i < len(y_positions):
                self._draw_capacitor_model(cap_name, cap_component, y_positions[i])
        
        plt.title("Analog Model for ACF1 Circuit", fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.show()
    
    def _draw_capacitor_model(self, cap_name: str, cap_component: CircuitComponent, y_pos: float) -> None:
        """Draw analog model for a single capacitor"""
        # Input voltage nodes
        input_node1 = self._get_voltage_name(cap_component.node1)
        input_node2 = self._get_voltage_name(cap_component.node2)
        
        # Starting positions
        x_start = 1
        x_current = x_start
        
        # Input voltage sources
        self._draw_voltage_source(x_current, y_pos, input_node1)
        x_current += 2
        
        # Subtraction block
        self._draw_subtraction_block(x_current, y_pos, input_node1, input_node2)
        x_current += 2
        
        # Voltage difference output
        v_diff = f"d{cap_name[1:].upper()}"  # dVcu, dVcv, dVcw
        self._draw_voltage_meter(x_current, y_pos, v_diff)
        x_current += 2
        
        # Integration block (1/dt)
        self._draw_integration_block(x_current, y_pos, "1/dt")
        x_current += 2
        
        # Capacitance multiplication
        self._draw_gain_block(x_current, y_pos, cap_component.value)
        x_current += 2
        
        # Current calculation output
        i_cap = f"I{cap_name[1:].lower()}_cal"  # Icu_cal, Icv_cal, Icw_cal
        self._draw_current_meter(x_current, y_pos, i_cap)
        x_current += 2
        
        # Integration for voltage
        self._draw_integration_block(x_current, y_pos, f"1/{cap_component.value}")
        x_current += 2
        
        # Voltage calculation output
        v_cap = f"V{cap_name[1:].lower()}_cal"  # Vcu_cal, Vcv_cal, Vcw_cal
        self._draw_voltage_meter(x_current, y_pos, v_cap)
        x_current += 2
        
        # Final output
        v_cap_out = f"V_{cap_name[1:].lower()}_cal"
        self._draw_output_terminal(x_current, y_pos, v_cap_out)
        
        # Draw connections
        self._draw_connections(x_start, y_pos, x_current)
        
        # Add ground connection for second input
        self._draw_voltage_source(x_start, y_pos - 1, input_node2)
    
    def _draw_voltage_source(self, x: float, y: float, label: str) -> None:
        """Draw voltage source symbol"""
        # Draw voltage source rectangle
        rect = FancyBboxPatch((x-0.3, y-0.3), 0.6, 0.6, 
                             boxstyle="round,pad=0.05", 
                             facecolor='lightblue', 
                             edgecolor='black')
        self.ax.add_patch(rect)
        
        # Add voltage symbol
        circle = Circle((x, y), 0.15, facecolor='white', edgecolor='black')
        self.ax.add_patch(circle)
        self.ax.text(x, y, 'V', ha='center', va='center', fontsize=8, fontweight='bold')
        
        # Add label
        self.ax.text(x, y-0.6, label, ha='center', va='center', fontsize=8)
    
    def _draw_subtraction_block(self, x: float, y: float, input1: str, input2: str) -> None:
        """Draw subtraction block"""
        # Draw circle
        circle = Circle((x, y), 0.3, facecolor='white', edgecolor='black', linewidth=2)
        self.ax.add_patch(circle)
        
        # Add + and - symbols
        self.ax.text(x-0.15, y+0.1, '+', ha='center', va='center', fontsize=12, fontweight='bold')
        self.ax.text(x-0.15, y-0.1, '-', ha='center', va='center', fontsize=12, fontweight='bold')
        
        # Add input labels
        self.ax.text(x-0.8, y+0.1, input1, ha='center', va='center', fontsize=8)
        self.ax.text(x-0.8, y-0.1, input2, ha='center', va='center', fontsize=8)
    
    def _draw_voltage_meter(self, x: float, y: float, label: str) -> None:
        """Draw voltage meter"""
        circle = Circle((x, y), 0.25, facecolor='lightyellow', edgecolor='black')
        self.ax.add_patch(circle)
        self.ax.text(x, y, 'V', ha='center', va='center', fontsize=10, fontweight='bold')
        self.ax.text(x, y-0.5, label, ha='center', va='center', fontsize=8)
    
    def _draw_current_meter(self, x: float, y: float, label: str) -> None:
        """Draw current meter"""
        circle = Circle((x, y), 0.25, facecolor='lightgreen', edgecolor='black')
        self.ax.add_patch(circle)
        self.ax.text(x, y, 'I', ha='center', va='center', fontsize=10, fontweight='bold')
        self.ax.text(x, y-0.5, label, ha='center', va='center', fontsize=8)
    
    def _draw_integration_block(self, x: float, y: float, label: str) -> None:
        """Draw integration block"""
        rect = FancyBboxPatch((x-0.4, y-0.25), 0.8, 0.5, 
                             boxstyle="round,pad=0.05", 
                             facecolor='lightcoral', 
                             edgecolor='black')
        self.ax.add_patch(rect)
        self.ax.text(x, y, label, ha='center', va='center', fontsize=9, fontweight='bold')
    
    def _draw_gain_block(self, x: float, y: float, gain: str) -> None:
        """Draw gain block"""
        # Draw triangle (gain block)
        triangle = patches.Polygon([(x-0.3, y-0.2), (x-0.3, y+0.2), (x+0.3, y)], 
                                 closed=True, facecolor='lightgray', edgecolor='black')
        self.ax.add_patch(triangle)
        self.ax.text(x-0.1, y, gain, ha='center', va='center', fontsize=8, fontweight='bold')
    
    def _draw_output_terminal(self, x: float, y: float, label: str) -> None:
        """Draw output terminal"""
        rect = FancyBboxPatch((x-0.3, y-0.2), 0.6, 0.4, 
                             boxstyle="round,pad=0.05", 
                             facecolor='lightsteelblue', 
                             edgecolor='black')
        self.ax.add_patch(rect)
        self.ax.text(x, y+0.4, label, ha='center', va='center', fontsize=8)
    
    def _draw_connections(self, x_start: float, y: float, x_end: float) -> None:
        """Draw connection lines between blocks"""
        # Main horizontal line
        self.ax.plot([x_start + 0.3, x_end - 0.3], [y, y], 'k-', linewidth=1.5)
        
        # Connection points (small circles)
        connection_points = np.arange(x_start + 1.5, x_end, 2)
        for x_point in connection_points:
            circle = Circle((x_point, y), 0.05, facecolor='black', edgecolor='black')
            self.ax.add_patch(circle)
    
    def _get_voltage_name(self, node_id: int) -> str:
        """Get voltage name for a node"""
        if node_id == 0:
            return "GND"  # Ground reference
        elif node_id == 1:
            return "Vu1"
        elif node_id == 2:
            return "Vv1"
        elif node_id == 3:
            return "Vw1"
        elif node_id == 4:
            return "Vn1"  # Common node
        else:
            return f"Vn{node_id}"

if __name__ == "__main__":
    # Test the analog model generator
    from circuit_parser import CircuitParser
    from equation_generator import EquationGenerator
    
    parser = CircuitParser()
    parser.parse_file("input/ACF1.cct")
    
    eq_generator = EquationGenerator(parser)
    eq_generator.generate_capacitor_equations()
    
    model_generator = AnalogModelGenerator(parser, eq_generator)
    model_generator.generate_analog_model("output/analog_model.png")
