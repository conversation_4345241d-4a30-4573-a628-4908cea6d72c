"""
Advanced Main program for Circuit Analysis
Processes .cct files and generates comprehensive mathematical equations
Supports R, L, C components with special handling for ACF2-type circuits
"""

import os
import argparse
from pathlib import Path
from advanced_circuit_parser import AdvancedCircuitParser
from advanced_equation_generator import AdvancedEquationGenerator

def create_output_directory():
    """Create output directory if it doesn't exist"""
    if not os.path.exists("output"):
        os.makedirs("output")

def process_circuit_file(input_file: str, generate_code: bool = False, generate_latex: bool = True):
    """Process a circuit file and generate comprehensive outputs"""
    
    print(f"Processing circuit file: {input_file}")
    print("="*60)
    
    # Parse the circuit file
    parser = AdvancedCircuitParser()
    try:
        parser.parse_file(input_file)
        print("✓ Circuit file parsed successfully")
    except Exception as e:
        print(f"✗ Error parsing circuit file: {e}")
        return
    
    # Display circuit information
    parser.print_circuit_info()
    print("\n" + "="*60 + "\n")
    
    # Generate equations
    eq_generator = AdvancedEquationGenerator(parser, input_file)
    try:
        equations = eq_generator.generate_all_equations()
        print("✓ Equations generated successfully")
    except Exception as e:
        print(f"✗ Error generating equations: {e}")
        return
    
    # Create output directory
    create_output_directory()

    # Extract filename without extension for output file naming
    input_path = Path(input_file)
    file_stem = input_path.stem  # Gets filename without extension

    # Generate text equations
    text_equations = eq_generator.generate_text_equations()
    print(text_equations)

    # Save text equations to file with filename included
    output_txt_file = f"output/advanced_equations_{file_stem}.txt"
    with open(output_txt_file, "w", encoding="utf-8") as f:
        f.write(text_equations)
    print(f"✓ Text equations saved to {output_txt_file}")
    
    # Generate ACF2 special equations if applicable
    special_equations = eq_generator.generate_acf2_special_equations()
    if special_equations:
        special_text = "\n".join(special_equations)
        print("\n" + special_text)

        # Save special equations with filename included
        output_special_file = f"output/special_equations_{file_stem}.txt"
        with open(output_special_file, "w", encoding="utf-8") as f:
            f.write(special_text)
        print(f"✓ Special equations saved to {output_special_file}")

    # Generate LaTeX equations
    if generate_latex:
        latex_equations = generate_latex_equations(eq_generator, input_file)
        output_tex_file = f"output/advanced_equations_{file_stem}.tex"
        with open(output_tex_file, "w", encoding="utf-8") as f:
            f.write(latex_equations)
        print(f"✓ LaTeX equations saved to {output_tex_file}")
    
    # Python code generation is disabled
    if generate_code:
        print("✓ Python code generation is disabled (equations only)")
    else:
        print("✓ Focus on equation formulation only")

def generate_latex_equations(eq_generator: AdvancedEquationGenerator, input_filename: str = "") -> str:
    """Generate LaTeX formatted equations"""
    latex_output = []
    latex_output.append("\\documentclass{article}")
    latex_output.append("\\usepackage{amsmath}")
    latex_output.append("\\usepackage{amssymb}")
    latex_output.append("\\usepackage{array}")
    latex_output.append("\\begin{document}")
    latex_output.append("\\title{Advanced Circuit Equations}")
    if input_filename:
        latex_output.append(f"\\author{{Source file: {input_filename}}}")
    latex_output.append("\\maketitle")
    
    # Component equations
    latex_output.append("\\section{Component Equations}")
    
    for comp_name, eq in eq_generator.component_equations.items():
        latex_output.append(f"\\subsection{{{eq.component_type} {comp_name}}}")
        latex_output.append("\\begin{align}")
        
        # Convert equations to LaTeX format
        voltage_eq_latex = eq.voltage_equation.replace("_", "_{").replace("V_", "V_{") + "}"
        current_eq_latex = eq.current_equation_discrete.replace("_", "_{").replace("I_", "I_{") + "}"
        
        latex_output.append(f"{voltage_eq_latex} \\\\")
        latex_output.append(f"{current_eq_latex}")
        
        latex_output.append("\\end{align}")
    
    # KCL equations
    if eq_generator.kcl_equations:
        latex_output.append("\\section{Kirchhoff's Current Law}")
        latex_output.append("\\begin{align}")
        for kcl_eq in eq_generator.kcl_equations:
            # Convert to LaTeX format
            latex_kcl = kcl_eq.replace("_", "_{").replace("I_", "I_{") + "}"
            latex_output.append(f"{latex_kcl} \\\\")
        latex_output.append("\\end{align}")
    
    # ACF2 special equations
    special_equations = eq_generator.generate_acf2_special_equations()
    if special_equations:
        latex_output.append("\\section{ACF2 Special Analysis}")
        latex_output.append("\\subsection{Matrix Form}")
        latex_output.append("\\begin{equation}")
        latex_output.append("\\begin{pmatrix}")
        latex_output.append("\\frac{dI_{Lu1}}{dt} \\\\")
        latex_output.append("\\frac{dI_{Lv1}}{dt} \\\\")
        latex_output.append("\\frac{dI_{Lw1}}{dt}")
        latex_output.append("\\end{pmatrix}")
        latex_output.append("= \\frac{1}{4L_1}")
        latex_output.append("\\begin{pmatrix}")
        latex_output.append("3 & -1 & -1 \\\\")
        latex_output.append("-1 & 3 & -1 \\\\")
        latex_output.append("-1 & -1 & 3")
        latex_output.append("\\end{pmatrix}")
        latex_output.append("\\begin{pmatrix}")
        latex_output.append("V_{u1} - V_{n1} - V_{cu2} - V_{Ru1} \\\\")
        latex_output.append("V_{v1} - V_{n1} - V_{cv2} - V_{Rv1} \\\\")
        latex_output.append("V_{w1} - V_{n1} - V_{cw2} - V_{Rw1}")
        latex_output.append("\\end{pmatrix}")
        latex_output.append("\\end{equation}")
    
    latex_output.append("\\end{document}")
    return "\n".join(latex_output)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Advanced Circuit Analysis and Equation Generator")
    parser.add_argument("input_file", nargs="?", default="input/ACF2.cct", 
                       help="Input .cct circuit file (default: input/ACF2.cct)")
    parser.add_argument("--with-code", action="store_true",
                       help="Enable Python code generation (disabled by default)")
    parser.add_argument("--no-latex", action="store_true",
                       help="Skip LaTeX generation")
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' not found")
        return
    
    # Process the circuit file
    process_circuit_file(
        args.input_file,
        generate_code=args.with_code,
        generate_latex=not args.no_latex
    )
    
    print("\n" + "="*60)
    print("Advanced processing completed!")
    # Get filename for output messages
    input_path = Path(args.input_file)
    file_stem = input_path.stem

    print("Output files generated in 'output/' directory:")
    print(f"  - advanced_equations_{file_stem}.txt (Comprehensive text equations)")
    if not args.no_latex:
        print(f"  - advanced_equations_{file_stem}.tex (LaTeX format equations)")
    if args.with_code:
        print("  - advanced_simulation_code.py (Python simulation code)")

    # Check for special equations
    special_file = f"output/special_equations_{file_stem}.txt"
    if os.path.exists(special_file):
        print(f"  - special_equations_{file_stem}.txt (Special circuit analysis)")

    print("\nNote: Python code generation is disabled by default.")
    print("      Focus is on mathematical equation formulation only.")

if __name__ == "__main__":
    main()
