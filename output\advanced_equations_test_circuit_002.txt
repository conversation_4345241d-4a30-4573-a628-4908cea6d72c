=== ADVANCED CIRCUIT EQUATIONS ===
Source file: input/test_circuit_002.cct

Resistor Ru1 (R = Ru1):
  Nodes: 1 - 4
  Voltage: V_ru1 = Vn1 - Vn4
  Current (continuous): I_ru1 = V_ru1 / Ru1
  Current (discrete): I_ru1 = (Vn1 - Vn4) / Ru1

Resistor Rv1 (R = Rv1):
  Nodes: 2 - 5
  Voltage: V_rv1 = Vn2 - Vn5
  Current (continuous): I_rv1 = V_rv1 / Rv1
  Current (discrete): I_rv1 = (Vn2 - Vn5) / Rv1

Resistor Rw1 (R = Rw1):
  Nodes: 3 - 6
  Voltage: V_rw1 = Vn3 - Vn6
  Current (continuous): I_rw1 = V_rw1 / Rw1
  Current (discrete): I_rw1 = (Vn3 - Vn6) / Rw1

Resistor Ru2 (R = Ru2):
  Nodes: 7 - 10
  Voltage: V_ru2 = Vn7 - Vn10
  Current (continuous): I_ru2 = V_ru2 / Ru2
  Current (discrete): I_ru2 = (Vn7 - Vn10) / Ru2

Resistor Rv2 (R = Rv2):
  Nodes: 8 - 12
  Voltage: V_rv2 = Vn8 - Vn12
  Current (continuous): I_rv2 = V_rv2 / Rv2
  Current (discrete): I_rv2 = (Vn8 - Vn12) / Rv2

Resistor Rw2 (R = Rw2):
  Nodes: 9 - 13
  Voltage: V_rw2 = Vn9 - Vn13
  Current (continuous): I_rw2 = V_rw2 / Rw2
  Current (discrete): I_rw2 = (Vn9 - Vn13) / Rw2

Inductor Lu1 (I = Lu1):
  Nodes: 4 - 7
  Voltage: V_lu1 = Vn4 - Vn7
  Current (continuous): V_lu1 = Lu1 * d(I_lu1)/dt
  Current (discrete): I_lu1 = I_lu1_old + ((Vn4 - Vn7) * dt) / Lu1
  Integration: I_lu1 = (1/Lu1) * ∫V_lu1 dt

Inductor Lv1 (I = Lv1):
  Nodes: 5 - 8
  Voltage: V_lv1 = Vn5 - Vn8
  Current (continuous): V_lv1 = Lv1 * d(I_lv1)/dt
  Current (discrete): I_lv1 = I_lv1_old + ((Vn5 - Vn8) * dt) / Lv1
  Integration: I_lv1 = (1/Lv1) * ∫V_lv1 dt

Inductor Lw1 (I = Lw1):
  Nodes: 6 - 9
  Voltage: V_lw1 = Vn6 - Vn9
  Current (continuous): V_lw1 = Lw1 * d(I_lw1)/dt
  Current (discrete): I_lw1 = I_lw1_old + ((Vn6 - Vn9) * dt) / Lw1
  Integration: I_lw1 = (1/Lw1) * ∫V_lw1 dt

Capacitor Cu1 (C = Cu1):
  Nodes: 10 - 11
  Voltage: V_cu1 = Vn10 - Vn11
  Current (continuous): I_cu1 = Cu1 * d(V_cu1)/dt
  Current (discrete): I_cu1 = Cu1 * ((Vn10 - Vn11) - V_cu1_old) / dt
  Integration: V_cu1 = (1/Cu1) * ∫I_cu1 dt

Capacitor Cv1 (C = Cv1):
  Nodes: 12 - 11
  Voltage: V_cv1 = Vn12 - Vn11
  Current (continuous): I_cv1 = Cv1 * d(V_cv1)/dt
  Current (discrete): I_cv1 = Cv1 * ((Vn12 - Vn11) - V_cv1_old) / dt
  Integration: V_cv1 = (1/Cv1) * ∫I_cv1 dt

Capacitor Cw1 (C = Cw1):
  Nodes: 13 - 11
  Voltage: V_cw1 = Vn13 - Vn11
  Current (continuous): I_cw1 = Cw1 * d(V_cw1)/dt
  Current (discrete): I_cw1 = Cw1 * ((Vn13 - Vn11) - V_cw1_old) / dt
  Integration: V_cw1 = (1/Cw1) * ∫I_cw1 dt

=== KIRCHHOFF'S CURRENT LAW (KCL) ===
Complete nodal analysis with component equations:
Each node shows:
  - All connected components and their types
  - Voltage equations for each component
  - Current-voltage relationships (Ohm's law, L/C dynamics)
  - Energy equations for reactive components
  - Current direction and KCL equation

Node 4 (Vn4):
  Connected components and their equations:
    Ru1 (R): 1→4
      Voltage: V_ru1 = Vn1 - Vn4
      Current (Ohm's law): I_ru1 = V_ru1 / Ru1
      Power: P_ru1 = V_ru1 * I_ru1 = V_ru1² / Ru1
      Current direction: incoming (I_ru1)

    Lu1 (L): 4→7
      Voltage: V_lu1 = Vn4 - Vn7
      Voltage-current relation: V_lu1 = Lu1 * d(I_lu1)/dt
      Current (continuous): d(I_lu1)/dt = V_lu1 / Lu1
      Current (discrete): I_lu1 = I_lu1_old + (V_lu1 * dt) / Lu1
      Energy: E_lu1 = (1/2) * Lu1 * I_lu1²
      Current direction: outgoing (I_lu1)

  Kirchhoff's Current Law (KCL):
    +I_ru1 -I_lu1 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 5 (Vn5):
  Connected components and their equations:
    Rv1 (R): 2→5
      Voltage: V_rv1 = Vn2 - Vn5
      Current (Ohm's law): I_rv1 = V_rv1 / Rv1
      Power: P_rv1 = V_rv1 * I_rv1 = V_rv1² / Rv1
      Current direction: incoming (I_rv1)

    Lv1 (L): 5→8
      Voltage: V_lv1 = Vn5 - Vn8
      Voltage-current relation: V_lv1 = Lv1 * d(I_lv1)/dt
      Current (continuous): d(I_lv1)/dt = V_lv1 / Lv1
      Current (discrete): I_lv1 = I_lv1_old + (V_lv1 * dt) / Lv1
      Energy: E_lv1 = (1/2) * Lv1 * I_lv1²
      Current direction: outgoing (I_lv1)

  Kirchhoff's Current Law (KCL):
    +I_rv1 -I_lv1 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 6 (Vn6):
  Connected components and their equations:
    Rw1 (R): 3→6
      Voltage: V_rw1 = Vn3 - Vn6
      Current (Ohm's law): I_rw1 = V_rw1 / Rw1
      Power: P_rw1 = V_rw1 * I_rw1 = V_rw1² / Rw1
      Current direction: incoming (I_rw1)

    Lw1 (L): 6→9
      Voltage: V_lw1 = Vn6 - Vn9
      Voltage-current relation: V_lw1 = Lw1 * d(I_lw1)/dt
      Current (continuous): d(I_lw1)/dt = V_lw1 / Lw1
      Current (discrete): I_lw1 = I_lw1_old + (V_lw1 * dt) / Lw1
      Energy: E_lw1 = (1/2) * Lw1 * I_lw1²
      Current direction: outgoing (I_lw1)

  Kirchhoff's Current Law (KCL):
    +I_rw1 -I_lw1 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 7 (Vn7):
  Connected components and their equations:
    Lu1 (L): 4→7
      Voltage: V_lu1 = Vn4 - Vn7
      Voltage-current relation: V_lu1 = Lu1 * d(I_lu1)/dt
      Current (continuous): d(I_lu1)/dt = V_lu1 / Lu1
      Current (discrete): I_lu1 = I_lu1_old + (V_lu1 * dt) / Lu1
      Energy: E_lu1 = (1/2) * Lu1 * I_lu1²
      Current direction: incoming (I_lu1)

    Ru2 (R): 7→10
      Voltage: V_ru2 = Vn7 - Vn10
      Current (Ohm's law): I_ru2 = V_ru2 / Ru2
      Power: P_ru2 = V_ru2 * I_ru2 = V_ru2² / Ru2
      Current direction: outgoing (I_ru2)

  Kirchhoff's Current Law (KCL):
    +I_lu1 -I_ru2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 8 (Vn8):
  Connected components and their equations:
    Lv1 (L): 5→8
      Voltage: V_lv1 = Vn5 - Vn8
      Voltage-current relation: V_lv1 = Lv1 * d(I_lv1)/dt
      Current (continuous): d(I_lv1)/dt = V_lv1 / Lv1
      Current (discrete): I_lv1 = I_lv1_old + (V_lv1 * dt) / Lv1
      Energy: E_lv1 = (1/2) * Lv1 * I_lv1²
      Current direction: incoming (I_lv1)

    Rv2 (R): 8→12
      Voltage: V_rv2 = Vn8 - Vn12
      Current (Ohm's law): I_rv2 = V_rv2 / Rv2
      Power: P_rv2 = V_rv2 * I_rv2 = V_rv2² / Rv2
      Current direction: outgoing (I_rv2)

  Kirchhoff's Current Law (KCL):
    +I_lv1 -I_rv2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 9 (Vn9):
  Connected components and their equations:
    Lw1 (L): 6→9
      Voltage: V_lw1 = Vn6 - Vn9
      Voltage-current relation: V_lw1 = Lw1 * d(I_lw1)/dt
      Current (continuous): d(I_lw1)/dt = V_lw1 / Lw1
      Current (discrete): I_lw1 = I_lw1_old + (V_lw1 * dt) / Lw1
      Energy: E_lw1 = (1/2) * Lw1 * I_lw1²
      Current direction: incoming (I_lw1)

    Rw2 (R): 9→13
      Voltage: V_rw2 = Vn9 - Vn13
      Current (Ohm's law): I_rw2 = V_rw2 / Rw2
      Power: P_rw2 = V_rw2 * I_rw2 = V_rw2² / Rw2
      Current direction: outgoing (I_rw2)

  Kirchhoff's Current Law (KCL):
    +I_lw1 -I_rw2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 10 (Vn10):
  Connected components and their equations:
    Cu1 (C): 10→11
      Voltage: V_cu1 = Vn10 - Vn11
      Current-voltage relation: I_cu1 = Cu1 * d(V_cu1)/dt
      Voltage (continuous): d(V_cu1)/dt = I_cu1 / Cu1
      Voltage (discrete): V_cu1 = V_cu1_old + (I_cu1 * dt) / Cu1
      Energy: E_cu1 = (1/2) * Cu1 * V_cu1²
      Current direction: outgoing (I_cu1)

    Ru2 (R): 7→10
      Voltage: V_ru2 = Vn7 - Vn10
      Current (Ohm's law): I_ru2 = V_ru2 / Ru2
      Power: P_ru2 = V_ru2 * I_ru2 = V_ru2² / Ru2
      Current direction: incoming (I_ru2)

  Kirchhoff's Current Law (KCL):
    -I_cu1 +I_ru2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 11 (GROUND) (Vn11):
  Connected components and their equations:
    Cu1 (C): 10→11
      Voltage: V_cu1 = Vn10 - Vn11
      Current-voltage relation: I_cu1 = Cu1 * d(V_cu1)/dt
      Voltage (continuous): d(V_cu1)/dt = I_cu1 / Cu1
      Voltage (discrete): V_cu1 = V_cu1_old + (I_cu1 * dt) / Cu1
      Energy: E_cu1 = (1/2) * Cu1 * V_cu1²
      Current direction: incoming (I_cu1)

    Cv1 (C): 12→11
      Voltage: V_cv1 = Vn12 - Vn11
      Current-voltage relation: I_cv1 = Cv1 * d(V_cv1)/dt
      Voltage (continuous): d(V_cv1)/dt = I_cv1 / Cv1
      Voltage (discrete): V_cv1 = V_cv1_old + (I_cv1 * dt) / Cv1
      Energy: E_cv1 = (1/2) * Cv1 * V_cv1²
      Current direction: incoming (I_cv1)

    Cw1 (C): 13→11
      Voltage: V_cw1 = Vn13 - Vn11
      Current-voltage relation: I_cw1 = Cw1 * d(V_cw1)/dt
      Voltage (continuous): d(V_cw1)/dt = I_cw1 / Cw1
      Voltage (discrete): V_cw1 = V_cw1_old + (I_cw1 * dt) / Cw1
      Energy: E_cw1 = (1/2) * Cw1 * V_cw1²
      Current direction: incoming (I_cw1)

  Kirchhoff's Current Law (KCL):
    +I_cu1 +I_cv1 +I_cw1 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 12 (Vn12):
  Connected components and their equations:
    Cv1 (C): 12→11
      Voltage: V_cv1 = Vn12 - Vn11
      Current-voltage relation: I_cv1 = Cv1 * d(V_cv1)/dt
      Voltage (continuous): d(V_cv1)/dt = I_cv1 / Cv1
      Voltage (discrete): V_cv1 = V_cv1_old + (I_cv1 * dt) / Cv1
      Energy: E_cv1 = (1/2) * Cv1 * V_cv1²
      Current direction: outgoing (I_cv1)

    Rv2 (R): 8→12
      Voltage: V_rv2 = Vn8 - Vn12
      Current (Ohm's law): I_rv2 = V_rv2 / Rv2
      Power: P_rv2 = V_rv2 * I_rv2 = V_rv2² / Rv2
      Current direction: incoming (I_rv2)

  Kirchhoff's Current Law (KCL):
    -I_cv1 +I_rv2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 13 (Vn13):
  Connected components and their equations:
    Cw1 (C): 13→11
      Voltage: V_cw1 = Vn13 - Vn11
      Current-voltage relation: I_cw1 = Cw1 * d(V_cw1)/dt
      Voltage (continuous): d(V_cw1)/dt = I_cw1 / Cw1
      Voltage (discrete): V_cw1 = V_cw1_old + (I_cw1 * dt) / Cw1
      Energy: E_cw1 = (1/2) * Cw1 * V_cw1²
      Current direction: outgoing (I_cw1)

    Rw2 (R): 9→13
      Voltage: V_rw2 = Vn9 - Vn13
      Current (Ohm's law): I_rw2 = V_rw2 / Rw2
      Power: P_rw2 = V_rw2 * I_rw2 = V_rw2² / Rw2
      Current direction: incoming (I_rw2)

  Kirchhoff's Current Law (KCL):
    -I_cw1 +I_rw2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

