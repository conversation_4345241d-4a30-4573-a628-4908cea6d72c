=== SPECIAL CIRCUIT ANALYSIS ===
Source file: input/ACF2.cct

Extended <PERSON><PERSON>'s Voltage Law (KVL) Analysis:
For each independent loop in the circuit:

Loop 1: Ru2 → Rv2 → Rw2
  KVL: V_ru2 + V_rv2 + V_rw2 = 0

Loop Current Analysis:
Independent current relationships:

  Node 1: -I_lu1 +I_ru1 = 0
  Node 2: +I_lu1 -I_lu2 -I_cu2 = 0
  Node 3: -I_lv1 +I_rv1 = 0
  Node 4: +I_lv1 -I_lv2 -I_cv2 = 0
  Node 5: -I_lw1 +I_rw1 = 0
  Node 6: +I_lw1 -I_lw2 -I_cw2 = 0
  Node 12: +I_lu2 -I_ru2 = 0
  Node 13: +I_lv2 -I_rv2 = 0
  Node 14: +I_lw2 -I_rw2 = 0

Energy Analysis:
Total system energy:

Magnetic energy (inductors):
  E_lu1 = (1/2) * Lu1 * I_lu1²
  E_lv1 = (1/2) * Lv1 * I_lv1²
  E_lw1 = (1/2) * Lw1 * I_lw1²
  E_ln1 = (1/2) * Ln1 * I_ln1²
  E_lu2 = (1/2) * Lu1 * I_lu2²
  E_lv2 = (1/2) * Lv1 * I_lv2²
  E_lw2 = (1/2) * Lw1 * I_lw2²
  E_ln2 = (1/2) * Ln1 * I_ln2²
  Total magnetic energy: E_L = E_lu1 + E_lv1 + E_lw1 + E_ln1 + E_lu2 + E_lv2 + E_lw2 + E_ln2

Electric energy (capacitors):
  E_cu2 = (1/2) * Cu1 * V_cu2²
  E_cv2 = (1/2) * Cv1 * V_cv2²
  E_cw2 = (1/2) * Cw1 * V_cw2²
  Total electric energy: E_C = E_cu2 + E_cv2 + E_cw2

Total system energy: E_total = E_L + E_C

System Matrix Analysis:

State vector:
x = [I_lu1, I_lv1, I_lw1, I_ln1, I_lu2, I_lv2, I_lw2, I_ln2, V_cu2, V_cv2, V_cw2]ᵀ

State-space representation:
dx/dt = A*x + B*u
y = C*x + D*u

Where:
  x = state vector (inductor currents, capacitor voltages)
  u = input vector (voltage sources, current sources)
  y = output vector (measured variables)
  A = system matrix (circuit topology dependent)
  B = input matrix
  C = output matrix
  D = feedthrough matrix

ACF-Type Circuit Specialized Analysis:

Extended Kirchhoff's Voltage Law (KVL) for ACF2_kawai circuit:
Primary phase equations:
VRu1 + VLu1 + Vcu2 + VLn1 = Vu1 - Vn1
VRv1 + VLv1 + Vcv2 + VLn1 = Vv1 - Vn1
VRw1 + VLw1 + Vcw2 + VLn1 = Vw1 - Vn1

Secondary phase equations:
Note: Secondary system shares the same voltage sources and capacitors as primary
VLu2 + VRu2 + VLn2 = VLu1 + VCu2 (from node 2 voltage balance)
VLv2 + VRv2 + VLn2 = VLv1 + VCv2 (from node 4 voltage balance)
VLw2 + VRw2 + VLn2 = VLw1 + VCw2 (from node 6 voltage balance)

Substituting primary equations:
VLu2 + VRu2 + VLn2 = (Vu1 - Vn1 - VRu1 - VLn1) + VCu2
VLv2 + VRv2 + VLn2 = (Vv1 - Vn1 - VRv1 - VLn1) + VCv2
VLw2 + VRw2 + VLn2 = (Vw1 - Vn1 - VRw1 - VLn1) + VCw2

Complete inductor current relationships:
Primary inductors: ILu1 + ILv1 + ILw1 = ILn1
Secondary inductors: ILu2 + ILv2 + ILw2 = ILn2
Node current conservation:
  Node 2: ILu1 = ILu2 + ICu2
  Node 4: ILv1 = ILv2 + ICv2
  Node 6: ILw1 = ILw2 + ICw2
  Node 11: ILn1 + ICu2 + ICv2 + ICw2 = ILn2

Complete inductor voltage equations:
Primary inductors:
VLu1 = Lu1 * d(ILu1)/dt
VLv1 = Lv1 * d(ILv1)/dt
VLw1 = Lw1 * d(ILw1)/dt
VLn1 = Ln1 * d(ILn1)/dt

Secondary inductors:
VLu2 = Lu2 * d(ILu2)/dt
VLv2 = Lv2 * d(ILv2)/dt
VLw2 = Lw2 * d(ILw2)/dt
VLn2 = Ln2 * d(ILn2)/dt

Using individual inductance values for all inductors:
Each inductor maintains its own inductance value

Complete matrix form representation (8×8 system with individual inductances):
⎡d(ILu1)/dt⎤   ⎡Lu1+Ln1  Ln1     Ln1     Ln1     0       0       0       0    ⎤⁻¹ ⎡VLu1⎤
⎢d(ILv1)/dt⎥   ⎢  Ln1   Lv1+Ln1  Ln1     Ln1     0       0       0       0    ⎥   ⎢VLv1⎥
⎢d(ILw1)/dt⎥   ⎢  Ln1     Ln1   Lw1+Ln1  Ln1     0       0       0       0    ⎥   ⎢VLw1⎥
⎢d(ILn1)/dt⎥ = ⎢  Ln1     Ln1     Ln1   Ln1+Ln2   0       0       0       Ln2  ⎥   ⎢VLn1⎥
⎢d(ILu2)/dt⎥   ⎢   0       0       0       0    Lu2+Ln2  Ln2     Ln2     Ln2  ⎥   ⎢VLu2⎥
⎢d(ILv2)/dt⎥   ⎢   0       0       0       0      Ln2   Lv2+Ln2  Ln2     Ln2  ⎥   ⎢VLv2⎥
⎢d(ILw2)/dt⎥   ⎢   0       0       0       0      Ln2     Ln2   Lw2+Ln2  Ln2  ⎥   ⎢VLw2⎥
⎣d(ILn2)/dt⎦   ⎣   0       0       0      Ln1     Ln2     Ln2     Ln2   Ln1+Ln2⎦   ⎣VLn2⎦

Primary system (3×3 matrix for Lu1, Lv1, Lw1 with individual inductances):
⎡d(ILu1)/dt⎤   ⎡Lu1+Ln1   Ln1     Ln1   ⎤⁻¹ ⎡Vu1 - Vn1 - Vcu2 - VRu1⎤
⎢d(ILv1)/dt⎥ = ⎢  Ln1   Lv1+Ln1   Ln1   ⎥   ⎢Vv1 - Vn1 - Vcv2 - VRv1⎥
⎣d(ILw1)/dt⎦   ⎣  Ln1     Ln1   Lw1+Ln1⎦   ⎣Vw1 - Vn1 - Vcw2 - VRw1⎦

Secondary system (3×3 matrix for Lu2, Lv2, Lw2):
Complete equations with all voltage components:
Note: Secondary side should have the same structure as primary side

If secondary side has independent voltage sources (Vu2, Vv2, Vw2):
⎡d(ILu2)/dt⎤   ⎡Lu2+Ln2   Ln2     Ln2   ⎤⁻¹ ⎡Vu2 - Vn2 - VCu2 - VRu2⎤
⎢d(ILv2)/dt⎥ = ⎢  Ln2   Lv2+Ln2   Ln2   ⎥   ⎢Vv2 - Vn2 - VCv2 - VRv2⎥
⎣d(ILw2)/dt⎦   ⎣  Ln2     Ln2   Lw2+Ln2⎦   ⎣Vw2 - Vn2 - VCw2 - VRw2⎦

If secondary side is coupled to primary (transformer-like behavior):
⎡d(ILu2)/dt⎤   ⎡Lu2+Ln2   Ln2     Ln2   ⎤⁻¹ ⎡k*Vu1 - Vn2 - VCu2 - VRu2⎤
⎢d(ILv2)/dt⎥ = ⎢  Ln2   Lv2+Ln2   Ln2   ⎥   ⎢k*Vv1 - Vn2 - VCv2 - VRv2⎥
⎣d(ILw2)/dt⎦   ⎣  Ln2     Ln2   Lw2+Ln2⎦   ⎣k*Vw1 - Vn2 - VCw2 - VRw2⎦

Where:
  k = coupling coefficient between primary and secondary
  Vu2, Vv2, Vw2 = secondary input voltages
  Vn2 = secondary neutral point voltage
  VCu2, VCv2, VCw2 = capacitor voltages (shared between primary and secondary)

IMPORTANT NOTE:
The absence of Vu2, Vv2, Vw2 in the current implementation suggests
that the secondary side is not independently driven but rather
magnetically coupled to the primary side through the inductors.
For a complete analysis, secondary voltage sources should be defined.

Current implementation uses coupled approach:
⎡d(ILu2)/dt⎤   ⎡Lu2+Ln2   Ln2     Ln2   ⎤⁻¹ ⎡Vu1 - Vn1 - VCu2 - VRu1 - VRu2 - VLn2⎤
⎢d(ILv2)/dt⎥ = ⎢  Ln2   Lv2+Ln2   Ln2   ⎥   ⎢Vv1 - Vn1 - VCv2 - VRv1 - VRv2 - VLn2⎥
⎣d(ILw2)/dt⎦   ⎣  Ln2     Ln2   Lw2+Ln2⎦   ⎣Vw1 - Vn1 - VCw2 - VRw1 - VRw2 - VLn2⎦

Alternative simplified form (using node voltages):
⎡d(ILu2)/dt⎤   ⎡Lu2+Ln2   Ln2     Ln2   ⎤⁻¹ ⎡Vn2 - Vn12 - VRu2⎤
⎢d(ILv2)/dt⎥ = ⎢  Ln2   Lv2+Ln2   Ln2   ⎥   ⎢Vn4 - Vn13 - VRv2⎥
⎣d(ILw2)/dt⎦   ⎣  Ln2     Ln2   Lw2+Ln2⎦   ⎣Vn6 - Vn14 - VRw2⎦
Note: This simplified form omits the coupling terms for clarity

EXPLANATION: Why Primary and Secondary systems have different voltage terms:

Primary system (Lu1, Lv1, Lw1):
  - Connected directly to external voltage sources (Vu1, Vv1, Vw1)
  - Includes input resistors (Ru1, Rv1, Rw1)
  - Includes capacitors (Cu2, Cv2, Cw2) to ground
  - Includes neutral inductor (Ln1)
  - Full voltage equation: Vu1 - VRu1 - VLu1 - VCu2 - VLn1 = 0

Secondary system (Lu2, Lv2, Lw2):
  - Connected to intermediate nodes (2, 4, 6) not external sources
  - Includes output resistors (Ru2, Rv2, Rw2)
  - Shares the same capacitors and neutral inductors as primary
  - Voltage sources are implicit through node voltage relationships
  - Simplified voltage equation: Vnode - Voutput - VRsecondary = 0

The apparent 'missing' voltage terms (Vu1, VCu2) in secondary system are:
  1. Vu1, Vv1, Vw1: External sources only affect primary system directly
  2. VCu2, VCv2, VCw2: Shared between systems, accounted in node voltages
  3. VLn1, VLn2: Different neutral inductors for each system

Neutral inductor equations:
d(ILn1)/dt = (VLn1) / Ln1 = (Vn10 - Vn11) / Ln1
d(ILn2)/dt = (VLn2) / Ln2 = (Vn11 - Vn15) / Ln2

Capacitor current relationships (KCL):
ICu2 = Ilu1 - Ilu2
ICv2 = Ilv1 - Ilv2
ICw2 = Ilw1 - Ilw2

Capacitor voltage equations:
Vcu2 = (1/Cu2) * ∫ICu2 dt
Vcv2 = (1/Cv2) * ∫ICv2 dt
Vcw2 = (1/Cw2) * ∫ICw2 dt
