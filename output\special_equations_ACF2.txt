=== SPECIAL CIRCUIT ANALYSIS ===
Source file: input/ACF2.cct

Extended <PERSON><PERSON><PERSON>'s Voltage Law (KVL) for ACF2_kawai circuit:
Primary phase equations:
VRu1 + VLu1 + Vcu2 + VLn1 = Vu1 - Vn1
VRv1 + VLv1 + Vcv2 + VLn1 = Vv1 - Vn1
VRw1 + VLw1 + Vcw2 + VLn1 = Vw1 - Vn1

Secondary phase equations:
Note: Secondary system shares the same voltage sources and capacitors as primary
VLu2 + VRu2 + VLn2 = VLu1 + VCu2 (from node 2 voltage balance)
VLv2 + VRv2 + VLn2 = VLv1 + VCv2 (from node 4 voltage balance)
VLw2 + VRw2 + VLn2 = VLw1 + VCw2 (from node 6 voltage balance)

Substituting primary equations:
VLu2 + VRu2 + VLn2 = (Vu1 - Vn1 - VRu1 - VLn1) + VCu2
VLv2 + VRv2 + VLn2 = (Vv1 - Vn1 - VRv1 - VLn1) + VCv2
VLw2 + VRw2 + VLn2 = (Vw1 - Vn1 - VRw1 - VLn1) + VCw2

Complete inductor current relationships:
Primary inductors: ILu1 + ILv1 + ILw1 = ILn1
Secondary inductors: ILu2 + ILv2 + ILw2 = ILn2
Node current conservation:
  Node 2: ILu1 = ILu2 + ICu2
  Node 4: ILv1 = ILv2 + ICv2
  Node 6: ILw1 = ILw2 + ICw2
  Node 11: ILn1 + ICu2 + ICv2 + ICw2 = ILn2

Complete inductor voltage equations:
Primary inductors:
VLu1 = Lu1 * d(ILu1)/dt
VLv1 = Lv1 * d(ILv1)/dt
VLw1 = Lw1 * d(ILw1)/dt
VLn1 = Ln1 * d(ILn1)/dt

Secondary inductors:
VLu2 = Lu2 * d(ILu2)/dt
VLv2 = Lv2 * d(ILv2)/dt
VLw2 = Lw2 * d(ILw2)/dt
VLn2 = Ln2 * d(ILn2)/dt

Assuming all inductances are equal (L1):
All inductors have the same inductance value L1

Complete matrix form representation (8×8 system):
⎡d(ILu1)/dt⎤   ⎡ 2  1  1  1  0  0  0  0⎤ ⎡VLu1⎤
⎢d(ILv1)/dt⎥   ⎢ 1  2  1  1  0  0  0  0⎥ ⎢VLv1⎥
⎢d(ILw1)/dt⎥   ⎢ 1  1  2  1  0  0  0  0⎥ ⎢VLw1⎥
⎢d(ILn1)/dt⎥ = ⎢ 1  1  1  2  0  0  0  0⎥ ⎢VLn1⎥ / L1
⎢d(ILu2)/dt⎥   ⎢ 0  0  0  0  2  1  1  1⎥ ⎢VLu2⎥
⎢d(ILv2)/dt⎥   ⎢ 0  0  0  0  1  2  1  1⎥ ⎢VLv2⎥
⎢d(ILw2)/dt⎥   ⎢ 0  0  0  0  1  1  2  1⎥ ⎢VLw2⎥
⎣d(ILn2)/dt⎦   ⎣ 0  0  0  0  1  1  1  2⎦ ⎣VLn2⎦

Simplified primary system (3×3 matrix for Lu1, Lv1, Lw1):
⎡d(ILu1)/dt⎤     1   ⎡ 3  -1  -1⎤ ⎡Vu1 - Vn1 - Vcu2 - VRu1⎤
⎢d(ILv1)/dt⎥ = ---- ⎢-1   3  -1⎥ ⎢Vv1 - Vn1 - Vcv2 - VRv1⎥
⎣d(ILw1)/dt⎦   4*L1 ⎣-1  -1   3⎦ ⎣Vw1 - Vn1 - Vcw2 - VRw1⎦

Secondary system (3×3 matrix for Lu2, Lv2, Lw2):
Complete equations with all voltage components:
Note: Secondary side should have the same structure as primary side

If secondary side has independent voltage sources (Vu2, Vv2, Vw2):
⎡d(ILu2)/dt⎤     1   ⎡ 3  -1  -1⎤ ⎡Vu2 - Vn2 - VCu2 - VRu2⎤
⎢d(ILv2)/dt⎥ = ---- ⎢-1   3  -1⎥ ⎢Vv2 - Vn2 - VCv2 - VRv2⎥
⎣d(ILw2)/dt⎦   4*L1 ⎣-1  -1   3⎦ ⎣Vw2 - Vn2 - VCw2 - VRw2⎦

If secondary side is coupled to primary (transformer-like behavior):
⎡d(ILu2)/dt⎤     1   ⎡ 3  -1  -1⎤ ⎡k*Vu1 - Vn2 - VCu2 - VRu2⎤
⎢d(ILv2)/dt⎥ = ---- ⎢-1   3  -1⎥ ⎢k*Vv1 - Vn2 - VCv2 - VRv2⎥
⎣d(ILw2)/dt⎦   4*L1 ⎣-1  -1   3⎦ ⎣k*Vw1 - Vn2 - VCw2 - VRw2⎦

Where:
  k = coupling coefficient between primary and secondary
  Vu2, Vv2, Vw2 = secondary input voltages
  Vn2 = secondary neutral point voltage
  VCu2, VCv2, VCw2 = capacitor voltages (shared between primary and secondary)

IMPORTANT NOTE:
The absence of Vu2, Vv2, Vw2 in the current implementation suggests
that the secondary side is not independently driven but rather
magnetically coupled to the primary side through the inductors.
For a complete analysis, secondary voltage sources should be defined.

Current implementation uses coupled approach:
⎡d(ILu2)/dt⎤     1   ⎡ 3  -1  -1⎤ ⎡Vu1 - Vn1 - VCu2 - VRu1 - VRu2 - VLn2⎤
⎢d(ILv2)/dt⎥ = ---- ⎢-1   3  -1⎥ ⎢Vv1 - Vn1 - VCv2 - VRv1 - VRv2 - VLn2⎥
⎣d(ILw2)/dt⎦   4*L1 ⎣-1  -1   3⎦ ⎣Vw1 - Vn1 - VCw2 - VRw1 - VRw2 - VLn2⎦

Alternative simplified form (using node voltages):
⎡d(ILu2)/dt⎤     1   ⎡ 3  -1  -1⎤ ⎡Vn2 - Vn12 - VRu2⎤
⎢d(ILv2)/dt⎥ = ---- ⎢-1   3  -1⎥ ⎢Vn4 - Vn13 - VRv2⎥
⎣d(ILw2)/dt⎦   4*L1 ⎣-1  -1   3⎦ ⎣Vn6 - Vn14 - VRw2⎦
Note: This simplified form omits the coupling terms for clarity

EXPLANATION: Why Primary and Secondary systems have different voltage terms:

Primary system (Lu1, Lv1, Lw1):
  - Connected directly to external voltage sources (Vu1, Vv1, Vw1)
  - Includes input resistors (Ru1, Rv1, Rw1)
  - Includes capacitors (Cu2, Cv2, Cw2) to ground
  - Includes neutral inductor (Ln1)
  - Full voltage equation: Vu1 - VRu1 - VLu1 - VCu2 - VLn1 = 0

Secondary system (Lu2, Lv2, Lw2):
  - Connected to intermediate nodes (2, 4, 6) not external sources
  - Includes output resistors (Ru2, Rv2, Rw2)
  - Shares the same capacitors and neutral inductors as primary
  - Voltage sources are implicit through node voltage relationships
  - Simplified voltage equation: Vnode - Voutput - VRsecondary = 0

The apparent 'missing' voltage terms (Vu1, VCu2) in secondary system are:
  1. Vu1, Vv1, Vw1: External sources only affect primary system directly
  2. VCu2, VCv2, VCw2: Shared between systems, accounted in node voltages
  3. VLn1, VLn2: Different neutral inductors for each system

Neutral inductor equations:
d(ILn1)/dt = (VLn1) / L1 = (Vn10 - Vn11) / L1
d(ILn2)/dt = (VLn2) / L1 = (Vn11 - Vn15) / L1

Capacitor current relationships (KCL):
ICu2 = Ilu1 - Ilu2
ICv2 = Ilv1 - Ilv2
ICw2 = Ilw1 - Ilw2

Capacitor voltage equations:
Vcu2 = (1/Cu2) * ∫ICu2 dt
Vcv2 = (1/Cv2) * ∫ICv2 dt
Vcw2 = (1/Cw2) * ∫ICw2 dt
