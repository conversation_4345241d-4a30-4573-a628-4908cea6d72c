=== ADVANCED CIRCUIT EQUATIONS ===
Source file: input/ACF2.cct

Resistor Ru2 (R = Ru1):
  Nodes: 12 - 16
  Voltage: V_ru2 = Vn12 - Vn16
  Current (continuous): I_ru2 = V_ru2 / Ru1
  Current (discrete): I_ru2 = (Vn12 - Vn16) / Ru1

Resistor Rv2 (R = Rv1):
  Nodes: 13 - 17
  Voltage: V_rv2 = Vn13 - Vn17
  Current (continuous): I_rv2 = V_rv2 / Rv1
  Current (discrete): I_rv2 = (Vn13 - Vn17) / Rv1

Resistor Rw2 (R = Rw1):
  Nodes: 14 - 18
  Voltage: V_rw2 = Vn14 - Vn18
  Current (continuous): I_rw2 = V_rw2 / Rw1
  Current (discrete): I_rw2 = (Vn14 - Vn18) / Rw1

Resistor Rw1 (R = Rw1):
  Nodes: 9 - 5
  Voltage: V_rw1 = Vn9 - Vn5
  Current (continuous): I_rw1 = V_rw1 / Rw1
  Current (discrete): I_rw1 = (Vn9 - Vn5) / Rw1

Resistor Rv1 (R = Rv1):
  Nodes: 8 - 3
  Voltage: V_rv1 = Vn8 - Vn3
  Current (continuous): I_rv1 = V_rv1 / Rv1
  Current (discrete): I_rv1 = (Vn8 - Vn3) / Rv1

Resistor Ru1 (R = Ru1):
  Nodes: 7 - 1
  Voltage: V_ru1 = Vn7 - Vn1
  Current (continuous): I_ru1 = V_ru1 / Ru1
  Current (discrete): I_ru1 = (Vn7 - Vn1) / Ru1

Inductor Lu1 (I = Lu1):
  Nodes: 1 - 2
  Voltage: V_lu1 = Vn1 - Vn2
  Current (continuous): V_lu1 = Lu1 * d(I_lu1)/dt
  Current (discrete): I_lu1 = I_lu1_old + ((Vn1 - Vn2) * dt) / Lu1
  Integration: I_lu1 = (1/Lu1) * ∫V_lu1 dt

Inductor Lv1 (I = Lv1):
  Nodes: 3 - 4
  Voltage: V_lv1 = Vn3 - Vn4
  Current (continuous): V_lv1 = Lv1 * d(I_lv1)/dt
  Current (discrete): I_lv1 = I_lv1_old + ((Vn3 - Vn4) * dt) / Lv1
  Integration: I_lv1 = (1/Lv1) * ∫V_lv1 dt

Inductor Lw1 (I = Lw1):
  Nodes: 5 - 6
  Voltage: V_lw1 = Vn5 - Vn6
  Current (continuous): V_lw1 = Lw1 * d(I_lw1)/dt
  Current (discrete): I_lw1 = I_lw1_old + ((Vn5 - Vn6) * dt) / Lw1
  Integration: I_lw1 = (1/Lw1) * ∫V_lw1 dt

Inductor Ln1 (I = Ln1):
  Nodes: 10 - 11
  Voltage: V_ln1 = Vn10 - Vn11
  Current (continuous): V_ln1 = Ln1 * d(I_ln1)/dt
  Current (discrete): I_ln1 = I_ln1_old + ((Vn10 - Vn11) * dt) / Ln1
  Integration: I_ln1 = (1/Ln1) * ∫V_ln1 dt

Inductor Lu2 (I = Lu1):
  Nodes: 2 - 12
  Voltage: V_lu2 = Vn2 - Vn12
  Current (continuous): V_lu2 = Lu1 * d(I_lu2)/dt
  Current (discrete): I_lu2 = I_lu2_old + ((Vn2 - Vn12) * dt) / Lu1
  Integration: I_lu2 = (1/Lu1) * ∫V_lu2 dt

Inductor Lv2 (I = Lv1):
  Nodes: 4 - 13
  Voltage: V_lv2 = Vn4 - Vn13
  Current (continuous): V_lv2 = Lv1 * d(I_lv2)/dt
  Current (discrete): I_lv2 = I_lv2_old + ((Vn4 - Vn13) * dt) / Lv1
  Integration: I_lv2 = (1/Lv1) * ∫V_lv2 dt

Inductor Lw2 (I = Lw1):
  Nodes: 6 - 14
  Voltage: V_lw2 = Vn6 - Vn14
  Current (continuous): V_lw2 = Lw1 * d(I_lw2)/dt
  Current (discrete): I_lw2 = I_lw2_old + ((Vn6 - Vn14) * dt) / Lw1
  Integration: I_lw2 = (1/Lw1) * ∫V_lw2 dt

Inductor Ln2 (I = Ln1):
  Nodes: 11 - 15
  Voltage: V_ln2 = Vn11 - Vn15
  Current (continuous): V_ln2 = Ln1 * d(I_ln2)/dt
  Current (discrete): I_ln2 = I_ln2_old + ((Vn11 - Vn15) * dt) / Ln1
  Integration: I_ln2 = (1/Ln1) * ∫V_ln2 dt

Capacitor Cu2 (C = Cu1):
  Nodes: 2 - 11
  Voltage: V_cu2 = Vn2 - Vn11
  Current (continuous): I_cu2 = Cu1 * d(V_cu2)/dt
  Current (discrete): I_cu2 = Cu1 * ((Vn2 - Vn11) - V_cu2_old) / dt
  Integration: V_cu2 = (1/Cu1) * ∫I_cu2 dt

Capacitor Cv2 (C = Cv1):
  Nodes: 4 - 11
  Voltage: V_cv2 = Vn4 - Vn11
  Current (continuous): I_cv2 = Cv1 * d(V_cv2)/dt
  Current (discrete): I_cv2 = Cv1 * ((Vn4 - Vn11) - V_cv2_old) / dt
  Integration: V_cv2 = (1/Cv1) * ∫I_cv2 dt

Capacitor Cw2 (C = Cw1):
  Nodes: 6 - 11
  Voltage: V_cw2 = Vn6 - Vn11
  Current (continuous): I_cw2 = Cw1 * d(V_cw2)/dt
  Current (discrete): I_cw2 = Cw1 * ((Vn6 - Vn11) - V_cw2_old) / dt
  Integration: V_cw2 = (1/Cw1) * ∫I_cw2 dt

=== KIRCHHOFF'S CURRENT LAW (KCL) ===
Complete nodal analysis with component equations:
Each node shows:
  - All connected components and their types
  - Voltage equations for each component
  - Current-voltage relationships (Ohm's law, L/C dynamics)
  - Energy equations for reactive components
  - Current direction and KCL equation

Node 1 (Vn1):
  Connected components and their equations:
    Lu1 (L): 1→2
      Voltage: V_lu1 = Vn1 - Vn2
      Voltage-current relation: V_lu1 = Lu1 * d(I_lu1)/dt
      Current (continuous): d(I_lu1)/dt = V_lu1 / Lu1
      Current (discrete): I_lu1 = I_lu1_old + (V_lu1 * dt) / Lu1
      Energy: E_lu1 = (1/2) * Lu1 * I_lu1²
      Current direction: outgoing (I_lu1)

    Ru1 (R): 7→1
      Voltage: V_ru1 = Vn7 - Vn1
      Current (Ohm's law): I_ru1 = V_ru1 / Ru1
      Power: P_ru1 = V_ru1 * I_ru1 = V_ru1² / Ru1
      Current direction: incoming (I_ru1)

  Kirchhoff's Current Law (KCL):
    -I_lu1 +I_ru1 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 2 (Vn2):
  Connected components and their equations:
    Lu1 (L): 1→2
      Voltage: V_lu1 = Vn1 - Vn2
      Voltage-current relation: V_lu1 = Lu1 * d(I_lu1)/dt
      Current (continuous): d(I_lu1)/dt = V_lu1 / Lu1
      Current (discrete): I_lu1 = I_lu1_old + (V_lu1 * dt) / Lu1
      Energy: E_lu1 = (1/2) * Lu1 * I_lu1²
      Current direction: incoming (I_lu1)

    Lu2 (L): 2→12
      Voltage: V_lu2 = Vn2 - Vn12
      Voltage-current relation: V_lu2 = Lu1 * d(I_lu2)/dt
      Current (continuous): d(I_lu2)/dt = V_lu2 / Lu1
      Current (discrete): I_lu2 = I_lu2_old + (V_lu2 * dt) / Lu1
      Energy: E_lu2 = (1/2) * Lu1 * I_lu2²
      Current direction: outgoing (I_lu2)

    Cu2 (C): 2→11
      Voltage: V_cu2 = Vn2 - Vn11
      Current-voltage relation: I_cu2 = Cu1 * d(V_cu2)/dt
      Voltage (continuous): d(V_cu2)/dt = I_cu2 / Cu1
      Voltage (discrete): V_cu2 = V_cu2_old + (I_cu2 * dt) / Cu1
      Energy: E_cu2 = (1/2) * Cu1 * V_cu2²
      Current direction: outgoing (I_cu2)

  Kirchhoff's Current Law (KCL):
    +I_lu1 -I_lu2 -I_cu2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 3 (Vn3):
  Connected components and their equations:
    Lv1 (L): 3→4
      Voltage: V_lv1 = Vn3 - Vn4
      Voltage-current relation: V_lv1 = Lv1 * d(I_lv1)/dt
      Current (continuous): d(I_lv1)/dt = V_lv1 / Lv1
      Current (discrete): I_lv1 = I_lv1_old + (V_lv1 * dt) / Lv1
      Energy: E_lv1 = (1/2) * Lv1 * I_lv1²
      Current direction: outgoing (I_lv1)

    Rv1 (R): 8→3
      Voltage: V_rv1 = Vn8 - Vn3
      Current (Ohm's law): I_rv1 = V_rv1 / Rv1
      Power: P_rv1 = V_rv1 * I_rv1 = V_rv1² / Rv1
      Current direction: incoming (I_rv1)

  Kirchhoff's Current Law (KCL):
    -I_lv1 +I_rv1 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 4 (Vn4):
  Connected components and their equations:
    Lv1 (L): 3→4
      Voltage: V_lv1 = Vn3 - Vn4
      Voltage-current relation: V_lv1 = Lv1 * d(I_lv1)/dt
      Current (continuous): d(I_lv1)/dt = V_lv1 / Lv1
      Current (discrete): I_lv1 = I_lv1_old + (V_lv1 * dt) / Lv1
      Energy: E_lv1 = (1/2) * Lv1 * I_lv1²
      Current direction: incoming (I_lv1)

    Lv2 (L): 4→13
      Voltage: V_lv2 = Vn4 - Vn13
      Voltage-current relation: V_lv2 = Lv1 * d(I_lv2)/dt
      Current (continuous): d(I_lv2)/dt = V_lv2 / Lv1
      Current (discrete): I_lv2 = I_lv2_old + (V_lv2 * dt) / Lv1
      Energy: E_lv2 = (1/2) * Lv1 * I_lv2²
      Current direction: outgoing (I_lv2)

    Cv2 (C): 4→11
      Voltage: V_cv2 = Vn4 - Vn11
      Current-voltage relation: I_cv2 = Cv1 * d(V_cv2)/dt
      Voltage (continuous): d(V_cv2)/dt = I_cv2 / Cv1
      Voltage (discrete): V_cv2 = V_cv2_old + (I_cv2 * dt) / Cv1
      Energy: E_cv2 = (1/2) * Cv1 * V_cv2²
      Current direction: outgoing (I_cv2)

  Kirchhoff's Current Law (KCL):
    +I_lv1 -I_lv2 -I_cv2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 5 (Vn5):
  Connected components and their equations:
    Lw1 (L): 5→6
      Voltage: V_lw1 = Vn5 - Vn6
      Voltage-current relation: V_lw1 = Lw1 * d(I_lw1)/dt
      Current (continuous): d(I_lw1)/dt = V_lw1 / Lw1
      Current (discrete): I_lw1 = I_lw1_old + (V_lw1 * dt) / Lw1
      Energy: E_lw1 = (1/2) * Lw1 * I_lw1²
      Current direction: outgoing (I_lw1)

    Rw1 (R): 9→5
      Voltage: V_rw1 = Vn9 - Vn5
      Current (Ohm's law): I_rw1 = V_rw1 / Rw1
      Power: P_rw1 = V_rw1 * I_rw1 = V_rw1² / Rw1
      Current direction: incoming (I_rw1)

  Kirchhoff's Current Law (KCL):
    -I_lw1 +I_rw1 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 6 (Vn6):
  Connected components and their equations:
    Lw1 (L): 5→6
      Voltage: V_lw1 = Vn5 - Vn6
      Voltage-current relation: V_lw1 = Lw1 * d(I_lw1)/dt
      Current (continuous): d(I_lw1)/dt = V_lw1 / Lw1
      Current (discrete): I_lw1 = I_lw1_old + (V_lw1 * dt) / Lw1
      Energy: E_lw1 = (1/2) * Lw1 * I_lw1²
      Current direction: incoming (I_lw1)

    Lw2 (L): 6→14
      Voltage: V_lw2 = Vn6 - Vn14
      Voltage-current relation: V_lw2 = Lw1 * d(I_lw2)/dt
      Current (continuous): d(I_lw2)/dt = V_lw2 / Lw1
      Current (discrete): I_lw2 = I_lw2_old + (V_lw2 * dt) / Lw1
      Energy: E_lw2 = (1/2) * Lw1 * I_lw2²
      Current direction: outgoing (I_lw2)

    Cw2 (C): 6→11
      Voltage: V_cw2 = Vn6 - Vn11
      Current-voltage relation: I_cw2 = Cw1 * d(V_cw2)/dt
      Voltage (continuous): d(V_cw2)/dt = I_cw2 / Cw1
      Voltage (discrete): V_cw2 = V_cw2_old + (I_cw2 * dt) / Cw1
      Energy: E_cw2 = (1/2) * Cw1 * V_cw2²
      Current direction: outgoing (I_cw2)

  Kirchhoff's Current Law (KCL):
    +I_lw1 -I_lw2 -I_cw2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 11 (GROUND) (Vn11):
  Connected components and their equations:
    Ln1 (L): 10→11
      Voltage: V_ln1 = Vn10 - Vn11
      Voltage-current relation: V_ln1 = Ln1 * d(I_ln1)/dt
      Current (continuous): d(I_ln1)/dt = V_ln1 / Ln1
      Current (discrete): I_ln1 = I_ln1_old + (V_ln1 * dt) / Ln1
      Energy: E_ln1 = (1/2) * Ln1 * I_ln1²
      Current direction: incoming (I_ln1)

    Ln2 (L): 11→15
      Voltage: V_ln2 = Vn11 - Vn15
      Voltage-current relation: V_ln2 = Ln1 * d(I_ln2)/dt
      Current (continuous): d(I_ln2)/dt = V_ln2 / Ln1
      Current (discrete): I_ln2 = I_ln2_old + (V_ln2 * dt) / Ln1
      Energy: E_ln2 = (1/2) * Ln1 * I_ln2²
      Current direction: outgoing (I_ln2)

    Cu2 (C): 2→11
      Voltage: V_cu2 = Vn2 - Vn11
      Current-voltage relation: I_cu2 = Cu1 * d(V_cu2)/dt
      Voltage (continuous): d(V_cu2)/dt = I_cu2 / Cu1
      Voltage (discrete): V_cu2 = V_cu2_old + (I_cu2 * dt) / Cu1
      Energy: E_cu2 = (1/2) * Cu1 * V_cu2²
      Current direction: incoming (I_cu2)

    Cv2 (C): 4→11
      Voltage: V_cv2 = Vn4 - Vn11
      Current-voltage relation: I_cv2 = Cv1 * d(V_cv2)/dt
      Voltage (continuous): d(V_cv2)/dt = I_cv2 / Cv1
      Voltage (discrete): V_cv2 = V_cv2_old + (I_cv2 * dt) / Cv1
      Energy: E_cv2 = (1/2) * Cv1 * V_cv2²
      Current direction: incoming (I_cv2)

    Cw2 (C): 6→11
      Voltage: V_cw2 = Vn6 - Vn11
      Current-voltage relation: I_cw2 = Cw1 * d(V_cw2)/dt
      Voltage (continuous): d(V_cw2)/dt = I_cw2 / Cw1
      Voltage (discrete): V_cw2 = V_cw2_old + (I_cw2 * dt) / Cw1
      Energy: E_cw2 = (1/2) * Cw1 * V_cw2²
      Current direction: incoming (I_cw2)

  Kirchhoff's Current Law (KCL):
    +I_ln1 -I_ln2 +I_cu2 +I_cv2 +I_cw2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 12 (Vn12):
  Connected components and their equations:
    Lu2 (L): 2→12
      Voltage: V_lu2 = Vn2 - Vn12
      Voltage-current relation: V_lu2 = Lu1 * d(I_lu2)/dt
      Current (continuous): d(I_lu2)/dt = V_lu2 / Lu1
      Current (discrete): I_lu2 = I_lu2_old + (V_lu2 * dt) / Lu1
      Energy: E_lu2 = (1/2) * Lu1 * I_lu2²
      Current direction: incoming (I_lu2)

    Ru2 (R): 12→16
      Voltage: V_ru2 = Vn12 - Vn16
      Current (Ohm's law): I_ru2 = V_ru2 / Ru1
      Power: P_ru2 = V_ru2 * I_ru2 = V_ru2² / Ru1
      Current direction: outgoing (I_ru2)

  Kirchhoff's Current Law (KCL):
    +I_lu2 -I_ru2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 13 (Vn13):
  Connected components and their equations:
    Lv2 (L): 4→13
      Voltage: V_lv2 = Vn4 - Vn13
      Voltage-current relation: V_lv2 = Lv1 * d(I_lv2)/dt
      Current (continuous): d(I_lv2)/dt = V_lv2 / Lv1
      Current (discrete): I_lv2 = I_lv2_old + (V_lv2 * dt) / Lv1
      Energy: E_lv2 = (1/2) * Lv1 * I_lv2²
      Current direction: incoming (I_lv2)

    Rv2 (R): 13→17
      Voltage: V_rv2 = Vn13 - Vn17
      Current (Ohm's law): I_rv2 = V_rv2 / Rv1
      Power: P_rv2 = V_rv2 * I_rv2 = V_rv2² / Rv1
      Current direction: outgoing (I_rv2)

  Kirchhoff's Current Law (KCL):
    +I_lv2 -I_rv2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 14 (Vn14):
  Connected components and their equations:
    Lw2 (L): 6→14
      Voltage: V_lw2 = Vn6 - Vn14
      Voltage-current relation: V_lw2 = Lw1 * d(I_lw2)/dt
      Current (continuous): d(I_lw2)/dt = V_lw2 / Lw1
      Current (discrete): I_lw2 = I_lw2_old + (V_lw2 * dt) / Lw1
      Energy: E_lw2 = (1/2) * Lw1 * I_lw2²
      Current direction: incoming (I_lw2)

    Rw2 (R): 14→18
      Voltage: V_rw2 = Vn14 - Vn18
      Current (Ohm's law): I_rw2 = V_rw2 / Rw1
      Power: P_rw2 = V_rw2 * I_rw2 = V_rw2² / Rw1
      Current direction: outgoing (I_rw2)

  Kirchhoff's Current Law (KCL):
    +I_lw2 -I_rw2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

