---
marp: true
theme: default
header: "Circuit Analysis and Equation Generator"
footer: "2025-08-22 <PERSON><PERSON><PERSON>"
paginate: true
---

# Circuit Analysis and Equation Generator

**回路解析と数式自動生成ツール**

---

## 📋 概要

このツールは、回路ファイル（.cct）から自動的に数学的方程式を生成し、R、L、C混在回路の包括的解析を行います。特に、キルヒホッフの法則に基づく詳細な電流・電圧関係の導出と、回路構成に応じた動的な解析が特徴です。

## 🎯 特徴
### ✨ 機能
- **完全ノード解析**: 各ノードの全接続素子と詳細方程式を生成
- **動的電流関係解析**: 回路構成を自動判定し、適切な電流関係式を生成
- **詳細KCL解析**: グラウンドノードを含む全ノードの完全解析
- **素子別詳細方程式**: R, L, C各素子の電流・電圧・エネルギー方程式
- **多形式出力**: テキスト、LaTeX、特殊解析レポート
---

## 🚀 クイックスタート

### 1. 依存関係のインストール
```bash
pip install -r requirements.txt
```

### 2. 基本的な使用方法
```bash
python main.py [input/<your_circuit_file>.cct] [オプション]
```

---

### 素子別詳細方程式

#### 抵抗器（R）
- **電流**: I_r = V_r / R（オームの法則）
- **電力**: P_r = V_r * I_r = V_r² / R

#### インダクタ（L）
- **電圧-電流関係**: V_l = L * d(I_l)/dt
- **離散時間**: I_l = I_l_old + (V_l * dt) / L
- **エネルギー**: E_l = (1/2) * L * I_l²

---

#### コンデンサ（C）
- **電流-電圧関係**: I_c = C * d(V_c)/dt
- **離散時間**: V_c = V_c_old + (I_c * dt) / C
- **エネルギー**: E_c = (1/2) * C * V_c²

---

## 📁 出力ファイル

### 生成される出力ファイル
| ファイル | 内容 | 特徴 |
|----------|------|------|
| `advanced_equations_*.txt` | 完全ノード解析 | 全素子の詳細方程式・KCL解析 |
| `advanced_equations_*.tex` | LaTeX形式 | 論文・レポート用 |
| `special_equations_*.txt` | 特殊解析 | 結合インダクタ・8×8行列解析 |

---

## 🔧 コマンドラインオプション

### 解析
```bash
python main.py [ファイル名] [オプション]
```

| オプション | 説明 |
|------------|------|
| `--no-latex` | LaTeX出力をスキップ |
| `--with-code` | Python コード生成を有効化（デフォルトは無効） |


---

## 📄 構文説明

| 要素 | 形式 | 例 |
|------|------|-----|
| 抵抗器 | `R 名前 ノード1 ノード2 値` | `R Ru1 1 2 Ru1` |
| インダクタ | `L 名前 ノード1 ノード2 値` | `L Lu1 2 3 Lu1` |
| コンデンサ | `C 名前 ノード1 ノード2 値` | `C Cu2 3 11 Cu2` |

---

## 🔄 Primary/Secondary結合システム解析

### 特殊解析

#### Primary System（1次側）
```
⎡d(ILu1)/dt⎤     1  ⎡ 3  -1  -1⎤ ⎡Vu1 - Vn1 - Vcu2 - VRu1⎤
⎢d(ILv1)/dt⎥ = ---- ⎢-1   3  -1⎥ ⎢Vv1 - Vn1 - Vcv2 - VRv1⎥
⎣d(ILw1)/dt⎦   4*L1 ⎣-1  -1   3⎦ ⎣Vw1 - Vn1 - Vcw2 - VRw1⎦
```

#### Secondary System（2次側）
```
⎡d(ILu2)/dt⎤     1  ⎡ 3  -1  -1⎤ ⎡Vn2 - Vn12 - VRu2⎤
⎢d(ILv2)/dt⎥ = ---- ⎢-1   3  -1⎥ ⎢Vn4 - Vn13 - VRv2⎥
⎣d(ILw2)/dt⎦   4*L1 ⎣-1  -1   3⎦ ⎣Vn6 - Vn14 - VRw2⎦
```

---

#### 電圧項の違いの説明

**Primary側に存在してSecondary側にない項:**
- **Vu1, Vv1, Vw1**: 外部電圧源（Secondary側は独立駆動されない）
- **Vcu2, Vcv2, Vcw2**: コンデンサ電圧（ノード電圧に含まれる）

**物理的意味:**
- Primary側: 外部電圧源による独立駆動
- Secondary側: Primary側からの磁気・容量結合による駆動
- 結合システム: 変圧器のような動作特性

---

#### 完全8×8行列システム
```
⎡d(ILu1)/dt⎤   ⎡ 2  1  1  1  0  0  0  0⎤ ⎡VLu1⎤
⎢d(ILv1)/dt⎥   ⎢ 1  2  1  1  0  0  0  0⎥ ⎢VLv1⎥
⎢d(ILw1)/dt⎥   ⎢ 1  1  2  1  0  0  0  0⎥ ⎢VLw1⎥
⎢d(ILn1)/dt⎥ = ⎢ 1  1  1  2  0  0  0  0⎥ ⎢VLn1⎥ / L1
⎢d(ILu2)/dt⎥   ⎢ 0  0  0  0  2  1  1  1⎥ ⎢VLu2⎥
⎢d(ILv2)/dt⎥   ⎢ 0  0  0  0  1  2  1  1⎥ ⎢VLv2⎥
⎢d(ILw2)/dt⎥   ⎢ 0  0  0  0  1  1  2  1⎥ ⎢VLw2⎥
⎣d(ILn2)/dt⎦   ⎣ 0  0  0  0  1  1  1  2⎦ ⎣VLn2⎦
```

---

## 🔬 技術的詳細

### アルゴリズム
1. **回路ファイル解析**: .cctファイルの構文解析
2. **トポロジー構築**: ノード・素子接続グラフの作成
3. **完全ノード解析**: 各ノードの全接続素子の詳細解析
4. **素子別方程式生成**: R, L, C各素子の詳細方程式
5. **動的解析**: 実際の回路構成に基づく関係式導出
6. **詳細KCL生成**: 各ノードの完全電流解析
7. **結合システム解析**: Primary/Secondary結合の特殊解析
8. **数式出力**: 複数形式での方程式生成

---

## 🔬 数学的基礎

### 基本法則
- **キルヒホッフの電流法則**: ∑I_incoming = ∑I_outgoing（全ノード適用）
- **キルヒホッフの電圧法則**: ∑V = 0（各ループ）
- **オームの法則**: V = IR, P = V²/R

### 素子方程式
- **抵抗器**: I = V/R, P = V²/R
- **インダクタ**: V = L × dI/dt, E = (1/2) × L × I²
- **コンデンサ**: I = C × dV/dt, E = (1/2) × C × V²

---

### 離散時間変換
- **インダクタ**: I = I_old + (V × dt) / L
- **コンデンサ**: V = V_old + (I × dt) / C

### 結合システム解析
- **磁気結合**: Primary/Secondary間の相互インダクタンス
- **容量結合**: 共有コンデンサによる結合
- **8×8行列システム**: 全インダクタの結合方程式

---

## 💻 開発環境

### 必要環境
- Python 3.7+
- matplotlib（グラフ描画）
- numpy（数値計算）

---

## 🔄 適用可能な回路パターン

### パターン1: 直列接続
```
R → L → C → Ground
結果: IC = IL
```

### パターン2: 分岐接続
```
     → L2 → R2
L1 →
     → C → Ground
結果: IC = IL1 - IL2
```

---

### パターン3: 複数グラウンド接続
```
L1 → C1 ↘
L2 → C2 → Ground
L3 → C3 ↗
結果: IC1 = IL1, IC2 = IL2, IC3 = IL3
```

---

## 🧮 生成される数式の詳細

### 抵抗器
```
電圧: V_r = Vnode1 - Vnode2
電流: I_r = V_r / R
離散: I_r = (Vnode1 - Vnode2) / R
```

### インダクタ
```
電圧: V_l = Vnode1 - Vnode2
電流: V_l = L × d(I_l)/dt
離散: I_l = I_l_old + (V_l × dt) / L
```

---

### コンデンサ
```
電圧: V_c = Vnode1 - Vnode2
電流: I_c = C × d(V_c)/dt
離散: I_c = C × ((V_c - V_c_old) / dt)
```

---

## 🧮 システム方程式

### キルヒホッフの電流法則（KCL）
各ノードで: ∑I_incoming = ∑I_outgoing

### キルヒホッフの電圧法則（KVL）
各ループで: ∑V = 0

---

### 結合インダクタの行列方程式（ACF2特殊解析）
```
⎡d(ILu1)/dt⎤     1  ⎡ 3  -1  -1⎤ ⎡Vu1 - Vn1 - Vcu2 - VRu1⎤
⎢d(ILv1)/dt⎥ = ---- ⎢-1   3  -1⎥ ⎢Vv1 - Vn1 - Vcv2 - VRv1⎥
⎣d(ILw1)/dt⎦   4*L1 ⎣-1  -1   3⎦ ⎣Vw1 - Vn1 - Vcw2 - VRw1⎦
```

---

## 📂 プロジェクト構造

```
📁 プロジェクトルート
├── 🚀 main.py                       # メインプログラム（高度解析機能）
├── 🔍 advanced_circuit_parser.py    # 高度回路解析エンジン
├── 📐 advanced_equation_generator.py # 高度数式生成エンジン
├── 🔍 circuit_parser.py             # 基本回路解析
├── 📐 equation_generator.py         # 基本数式生成
├── 🎨 analog_model_generator.py     # アナログモデル図生成
├── 📋 requirements.txt              # 依存関係
├── 📖 README.md                     # このファイル
├── 📁 input/                        # 回路ファイル
└── 📁 output/                       # 生成ファイル
```

