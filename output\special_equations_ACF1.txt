=== SPECIAL CIRCUIT ANALYSIS ===
Source file: input/ACF1.cct

Extended <PERSON>'s Voltage Law (KVL) Analysis:
For each independent loop in the circuit:

Loop 1: Cu1 → Cv1 → Cw1
  KVL: V_cu1 + V_cv1 + V_cw1 = 0

Loop Current Analysis:
Independent current relationships:


Energy Analysis:
Total system energy:

Electric energy (capacitors):
  E_cu1 = (1/2) * Cu1 * V_cu1²
  E_cv1 = (1/2) * Cv1 * V_cv1²
  E_cw1 = (1/2) * Cw1 * V_cw1²
  Total electric energy: E_C = E_cu1 + E_cv1 + E_cw1

Total system energy: E_total = E_C

System Matrix Analysis:

State vector:
x = [V_cu1, V_cv1, V_cw1]ᵀ

State-space representation:
dx/dt = A*x + B*u
y = C*x + D*u

Where:
  x = state vector (inductor currents, capacitor voltages)
  u = input vector (voltage sources, current sources)
  y = output vector (measured variables)
  A = system matrix (circuit topology dependent)
  B = input matrix
  C = output matrix
  D = feedthrough matrix
