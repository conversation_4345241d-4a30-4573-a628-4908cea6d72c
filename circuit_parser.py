"""
Circuit Parser for .cct files
Parses circuit files and generates mathematical equations for analog modeling
"""

import re
from typing import Dict, <PERSON>, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class ComponentType(Enum):
    CAPACITOR = "C"
    RESISTOR = "R"
    INDUCTOR = "L"
    VOLTAGE_SOURCE = "V"
    CURRENT_SOURCE = "I"

@dataclass
class CircuitComponent:
    """Represents a circuit component"""
    component_type: ComponentType
    name: str
    node1: int
    node2: int
    value: str
    additional_params: List[str]

@dataclass
class CircuitNode:
    """Represents a circuit node"""
    node_id: int
    voltage_name: str
    connected_components: List[str]

class CircuitParser:
    """Parser for .cct circuit files"""
    
    def __init__(self):
        self.components: Dict[str, CircuitComponent] = {}
        self.nodes: Dict[int, CircuitNode] = {}
        self.time_params: Optional[List[str]] = None
        self.version: Optional[str] = None
    
    def parse_file(self, filepath: str) -> None:
        """Parse a .cct file and extract circuit information"""
        with open(filepath, 'r') as file:
            lines = file.readlines()
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if line.startswith('.TIME'):
                self._parse_time_directive(line)
            elif line.startswith('.TI_DMC_VERSION'):
                self._parse_version_directive(line)
            else:
                self._parse_component_line(line)
        
        self._build_node_connections()
    
    def _parse_time_directive(self, line: str) -> None:
        """Parse .TIME directive"""
        parts = line.split()
        self.time_params = parts[1:]  # Skip '.TIME'
    
    def _parse_version_directive(self, line: str) -> None:
        """Parse .TI_DMC_VERSION directive"""
        parts = line.split()
        if len(parts) >= 2:
            self.version = parts[1]
    
    def _parse_component_line(self, line: str) -> None:
        """Parse a component line (C, R, L, V, I, etc.)"""
        parts = line.split()
        if len(parts) < 4:
            return
        
        component_type_str = parts[0]
        component_name = parts[1]
        node1 = int(parts[2])
        node2 = int(parts[3])
        
        # Try to map component type
        try:
            component_type = ComponentType(component_type_str)
        except ValueError:
            # Unknown component type, skip for now
            return
        
        # Extract value and additional parameters
        value = parts[4] if len(parts) > 4 else ""
        additional_params = parts[5:] if len(parts) > 5 else []
        
        component = CircuitComponent(
            component_type=component_type,
            name=component_name,
            node1=node1,
            node2=node2,
            value=value,
            additional_params=additional_params
        )
        
        self.components[component_name] = component
    
    def _build_node_connections(self) -> None:
        """Build node connection information"""
        # Initialize nodes
        all_nodes = set()
        for comp in self.components.values():
            all_nodes.add(comp.node1)
            all_nodes.add(comp.node2)
        
        for node_id in all_nodes:
            voltage_name = f"Vn{node_id}" if node_id != 0 else "GND"
            self.nodes[node_id] = CircuitNode(
                node_id=node_id,
                voltage_name=voltage_name,
                connected_components=[]
            )
        
        # Connect components to nodes
        for comp_name, comp in self.components.items():
            self.nodes[comp.node1].connected_components.append(comp_name)
            self.nodes[comp.node2].connected_components.append(comp_name)
    
    def get_capacitors(self) -> Dict[str, CircuitComponent]:
        """Get all capacitor components"""
        return {name: comp for name, comp in self.components.items() 
                if comp.component_type == ComponentType.CAPACITOR}
    
    def get_component_by_name(self, name: str) -> Optional[CircuitComponent]:
        """Get component by name"""
        return self.components.get(name)
    
    def print_circuit_info(self) -> None:
        """Print parsed circuit information"""
        print("=== Circuit Information ===")
        print(f"Version: {self.version}")
        print(f"Time parameters: {self.time_params}")
        print("\nComponents:")
        for name, comp in self.components.items():
            print(f"  {name}: {comp.component_type.value} between nodes {comp.node1}-{comp.node2}, value={comp.value}")
        print("\nNodes:")
        for node_id, node in self.nodes.items():
            print(f"  Node {node_id} ({node.voltage_name}): connected to {node.connected_components}")

if __name__ == "__main__":
    # Test the parser
    parser = CircuitParser()
    parser.parse_file("input/ACF1.cct")
    parser.print_circuit_info()
