\documentclass{article}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{array}
\begin{document}
\title{Advanced Circuit Equations}
\author{Source file: input/test_circuit_001.cct}
\maketitle
\section{Component Equations}
\subsection{Resistor Ru1}
\begin{align}
V_{{ru1 = Vn1 - Vn2} \\
I_{{ru1 = (Vn1 - Vn2) / Ru1}
\end{align}
\subsection{Resistor Rv1}
\begin{align}
V_{{rv1 = Vn7 - Vn8} \\
I_{{rv1 = (Vn7 - Vn8) / Rv1}
\end{align}
\subsection{Resistor Rw1}
\begin{align}
V_{{rw1 = Vn4 - Vn5} \\
I_{{rw1 = (Vn4 - Vn5) / Rw1}
\end{align}
\subsection{Resistor Ru2}
\begin{align}
V_{{ru2 = Vn3 - Vn11} \\
I_{{ru2 = (Vn3 - Vn11) / Ru2}
\end{align}
\subsection{Resistor Ru3}
\begin{align}
V_{{ru3 = Vn3 - Vn10} \\
I_{{ru3 = (Vn3 - Vn10) / Ru3}
\end{align}
\subsection{Resistor Rv2}
\begin{align}
V_{{rv2 = Vn9 - Vn12} \\
I_{{rv2 = (Vn9 - Vn12) / Rv2}
\end{align}
\subsection{Resistor Rw2}
\begin{align}
V_{{rw2 = Vn6 - Vn13} \\
I_{{rw2 = (Vn6 - Vn13) / Rw2}
\end{align}
\subsection{Resistor Rv3}
\begin{align}
V_{{rv3 = Vn9 - Vn10} \\
I_{{rv3 = (Vn9 - Vn10) / Rv3}
\end{align}
\subsection{Resistor Rw3}
\begin{align}
V_{{rw3 = Vn6 - Vn10} \\
I_{{rw3 = (Vn6 - Vn10) / Rw3}
\end{align}
\subsection{Inductor Lu1}
\begin{align}
V_{{lu1 = Vn2 - Vn3} \\
I_{{lu1 = I_{{lu1_{old + ((Vn2 - Vn3) * dt) / Lu1}
\end{align}
\subsection{Inductor Lv1}
\begin{align}
V_{{lv1 = Vn8 - Vn9} \\
I_{{lv1 = I_{{lv1_{old + ((Vn8 - Vn9) * dt) / Lv1}
\end{align}
\subsection{Inductor Lw1}
\begin{align}
V_{{lw1 = Vn5 - Vn6} \\
I_{{lw1 = I_{{lw1_{old + ((Vn5 - Vn6) * dt) / Lw1}
\end{align}
\subsection{Capacitor Cu1}
\begin{align}
V_{{cu1 = Vn11 - Vn10} \\
I_{{cu1 = Cu1 * ((Vn11 - Vn10) - V_{cu1_{old) / dt}
\end{align}
\subsection{Capacitor Cv1}
\begin{align}
V_{{cv1 = Vn12 - Vn10} \\
I_{{cv1 = Cv1 * ((Vn12 - Vn10) - V_{cv1_{old) / dt}
\end{align}
\subsection{Capacitor Cw1}
\begin{align}
V_{{cw1 = Vn13 - Vn10} \\
I_{{cw1 = Cw1 * ((Vn13 - Vn10) - V_{cw1_{old) / dt}
\end{align}
\section{Kirchhoff's Current Law}
\begin{align}
Node 2 (Vn2):} \\
  Connected components and their equations:} \\
    Ru1 (R): 1→2} \\
      Voltage: V_{ru1 = Vn1 - Vn2} \\
      Current (Ohm's law): I_{{ru1 = V_{ru1 / Ru1} \\
      Power: P_{ru1 = V_{ru1 * I_{{ru1 = V_{ru1² / Ru1} \\
      Current direction: incoming (I_{{ru1)} \\
} \\
    Lu1 (L): 2→3} \\
      Voltage: V_{lu1 = Vn2 - Vn3} \\
      Voltage-current relation: V_{lu1 = Lu1 * d(I_{{lu1)/dt} \\
      Current (continuous): d(I_{{lu1)/dt = V_{lu1 / Lu1} \\
      Current (discrete): I_{{lu1 = I_{{lu1_{old + (V_{lu1 * dt) / Lu1} \\
      Energy: E_{lu1 = (1/2) * Lu1 * I_{{lu1²} \\
      Current direction: outgoing (I_{{lu1)} \\
} \\
  Kirchhoff's Current Law (KCL):} \\
    +I_{{ru1 -I_{{lu1 = 0} \\
    Physical meaning: ∑I_{{incoming = ∑I_{{outgoing} \\
} \\
Node 3 (Vn3):} \\
  Connected components and their equations:} \\
    Lu1 (L): 2→3} \\
      Voltage: V_{lu1 = Vn2 - Vn3} \\
      Voltage-current relation: V_{lu1 = Lu1 * d(I_{{lu1)/dt} \\
      Current (continuous): d(I_{{lu1)/dt = V_{lu1 / Lu1} \\
      Current (discrete): I_{{lu1 = I_{{lu1_{old + (V_{lu1 * dt) / Lu1} \\
      Energy: E_{lu1 = (1/2) * Lu1 * I_{{lu1²} \\
      Current direction: incoming (I_{{lu1)} \\
} \\
    Ru2 (R): 3→11} \\
      Voltage: V_{ru2 = Vn3 - Vn11} \\
      Current (Ohm's law): I_{{ru2 = V_{ru2 / Ru2} \\
      Power: P_{ru2 = V_{ru2 * I_{{ru2 = V_{ru2² / Ru2} \\
      Current direction: outgoing (I_{{ru2)} \\
} \\
    Ru3 (R): 3→10} \\
      Voltage: V_{ru3 = Vn3 - Vn10} \\
      Current (Ohm's law): I_{{ru3 = V_{ru3 / Ru3} \\
      Power: P_{ru3 = V_{ru3 * I_{{ru3 = V_{ru3² / Ru3} \\
      Current direction: outgoing (I_{{ru3)} \\
} \\
  Kirchhoff's Current Law (KCL):} \\
    +I_{{lu1 -I_{{ru2 -I_{{ru3 = 0} \\
    Physical meaning: ∑I_{{incoming = ∑I_{{outgoing} \\
} \\
Node 5 (Vn5):} \\
  Connected components and their equations:} \\
    Rw1 (R): 4→5} \\
      Voltage: V_{rw1 = Vn4 - Vn5} \\
      Current (Ohm's law): I_{{rw1 = V_{rw1 / Rw1} \\
      Power: P_{rw1 = V_{rw1 * I_{{rw1 = V_{rw1² / Rw1} \\
      Current direction: incoming (I_{{rw1)} \\
} \\
    Lw1 (L): 5→6} \\
      Voltage: V_{lw1 = Vn5 - Vn6} \\
      Voltage-current relation: V_{lw1 = Lw1 * d(I_{{lw1)/dt} \\
      Current (continuous): d(I_{{lw1)/dt = V_{lw1 / Lw1} \\
      Current (discrete): I_{{lw1 = I_{{lw1_{old + (V_{lw1 * dt) / Lw1} \\
      Energy: E_{lw1 = (1/2) * Lw1 * I_{{lw1²} \\
      Current direction: outgoing (I_{{lw1)} \\
} \\
  Kirchhoff's Current Law (KCL):} \\
    +I_{{rw1 -I_{{lw1 = 0} \\
    Physical meaning: ∑I_{{incoming = ∑I_{{outgoing} \\
} \\
Node 6 (Vn6):} \\
  Connected components and their equations:} \\
    Lw1 (L): 5→6} \\
      Voltage: V_{lw1 = Vn5 - Vn6} \\
      Voltage-current relation: V_{lw1 = Lw1 * d(I_{{lw1)/dt} \\
      Current (continuous): d(I_{{lw1)/dt = V_{lw1 / Lw1} \\
      Current (discrete): I_{{lw1 = I_{{lw1_{old + (V_{lw1 * dt) / Lw1} \\
      Energy: E_{lw1 = (1/2) * Lw1 * I_{{lw1²} \\
      Current direction: incoming (I_{{lw1)} \\
} \\
    Rw2 (R): 6→13} \\
      Voltage: V_{rw2 = Vn6 - Vn13} \\
      Current (Ohm's law): I_{{rw2 = V_{rw2 / Rw2} \\
      Power: P_{rw2 = V_{rw2 * I_{{rw2 = V_{rw2² / Rw2} \\
      Current direction: outgoing (I_{{rw2)} \\
} \\
    Rw3 (R): 6→10} \\
      Voltage: V_{rw3 = Vn6 - Vn10} \\
      Current (Ohm's law): I_{{rw3 = V_{rw3 / Rw3} \\
      Power: P_{rw3 = V_{rw3 * I_{{rw3 = V_{rw3² / Rw3} \\
      Current direction: outgoing (I_{{rw3)} \\
} \\
  Kirchhoff's Current Law (KCL):} \\
    +I_{{lw1 -I_{{rw2 -I_{{rw3 = 0} \\
    Physical meaning: ∑I_{{incoming = ∑I_{{outgoing} \\
} \\
Node 8 (Vn8):} \\
  Connected components and their equations:} \\
    Rv1 (R): 7→8} \\
      Voltage: V_{rv1 = Vn7 - Vn8} \\
      Current (Ohm's law): I_{{rv1 = V_{rv1 / Rv1} \\
      Power: P_{rv1 = V_{rv1 * I_{{rv1 = V_{rv1² / Rv1} \\
      Current direction: incoming (I_{{rv1)} \\
} \\
    Lv1 (L): 8→9} \\
      Voltage: V_{lv1 = Vn8 - Vn9} \\
      Voltage-current relation: V_{lv1 = Lv1 * d(I_{{lv1)/dt} \\
      Current (continuous): d(I_{{lv1)/dt = V_{lv1 / Lv1} \\
      Current (discrete): I_{{lv1 = I_{{lv1_{old + (V_{lv1 * dt) / Lv1} \\
      Energy: E_{lv1 = (1/2) * Lv1 * I_{{lv1²} \\
      Current direction: outgoing (I_{{lv1)} \\
} \\
  Kirchhoff's Current Law (KCL):} \\
    +I_{{rv1 -I_{{lv1 = 0} \\
    Physical meaning: ∑I_{{incoming = ∑I_{{outgoing} \\
} \\
Node 9 (Vn9):} \\
  Connected components and their equations:} \\
    Lv1 (L): 8→9} \\
      Voltage: V_{lv1 = Vn8 - Vn9} \\
      Voltage-current relation: V_{lv1 = Lv1 * d(I_{{lv1)/dt} \\
      Current (continuous): d(I_{{lv1)/dt = V_{lv1 / Lv1} \\
      Current (discrete): I_{{lv1 = I_{{lv1_{old + (V_{lv1 * dt) / Lv1} \\
      Energy: E_{lv1 = (1/2) * Lv1 * I_{{lv1²} \\
      Current direction: incoming (I_{{lv1)} \\
} \\
    Rv2 (R): 9→12} \\
      Voltage: V_{rv2 = Vn9 - Vn12} \\
      Current (Ohm's law): I_{{rv2 = V_{rv2 / Rv2} \\
      Power: P_{rv2 = V_{rv2 * I_{{rv2 = V_{rv2² / Rv2} \\
      Current direction: outgoing (I_{{rv2)} \\
} \\
    Rv3 (R): 9→10} \\
      Voltage: V_{rv3 = Vn9 - Vn10} \\
      Current (Ohm's law): I_{{rv3 = V_{rv3 / Rv3} \\
      Power: P_{rv3 = V_{rv3 * I_{{rv3 = V_{rv3² / Rv3} \\
      Current direction: outgoing (I_{{rv3)} \\
} \\
  Kirchhoff's Current Law (KCL):} \\
    +I_{{lv1 -I_{{rv2 -I_{{rv3 = 0} \\
    Physical meaning: ∑I_{{incoming = ∑I_{{outgoing} \\
} \\
Node 10 (GROUND) (Vn10):} \\
  Connected components and their equations:} \\
    Cu1 (C): 11→10} \\
      Voltage: V_{cu1 = Vn11 - Vn10} \\
      Current-voltage relation: I_{{cu1 = Cu1 * d(V_{cu1)/dt} \\
      Voltage (continuous): d(V_{cu1)/dt = I_{{cu1 / Cu1} \\
      Voltage (discrete): V_{cu1 = V_{cu1_{old + (I_{{cu1 * dt) / Cu1} \\
      Energy: E_{cu1 = (1/2) * Cu1 * V_{cu1²} \\
      Current direction: incoming (I_{{cu1)} \\
} \\
    Ru3 (R): 3→10} \\
      Voltage: V_{ru3 = Vn3 - Vn10} \\
      Current (Ohm's law): I_{{ru3 = V_{ru3 / Ru3} \\
      Power: P_{ru3 = V_{ru3 * I_{{ru3 = V_{ru3² / Ru3} \\
      Current direction: incoming (I_{{ru3)} \\
} \\
    Cv1 (C): 12→10} \\
      Voltage: V_{cv1 = Vn12 - Vn10} \\
      Current-voltage relation: I_{{cv1 = Cv1 * d(V_{cv1)/dt} \\
      Voltage (continuous): d(V_{cv1)/dt = I_{{cv1 / Cv1} \\
      Voltage (discrete): V_{cv1 = V_{cv1_{old + (I_{{cv1 * dt) / Cv1} \\
      Energy: E_{cv1 = (1/2) * Cv1 * V_{cv1²} \\
      Current direction: incoming (I_{{cv1)} \\
} \\
    Cw1 (C): 13→10} \\
      Voltage: V_{cw1 = Vn13 - Vn10} \\
      Current-voltage relation: I_{{cw1 = Cw1 * d(V_{cw1)/dt} \\
      Voltage (continuous): d(V_{cw1)/dt = I_{{cw1 / Cw1} \\
      Voltage (discrete): V_{cw1 = V_{cw1_{old + (I_{{cw1 * dt) / Cw1} \\
      Energy: E_{cw1 = (1/2) * Cw1 * V_{cw1²} \\
      Current direction: incoming (I_{{cw1)} \\
} \\
    Rv3 (R): 9→10} \\
      Voltage: V_{rv3 = Vn9 - Vn10} \\
      Current (Ohm's law): I_{{rv3 = V_{rv3 / Rv3} \\
      Power: P_{rv3 = V_{rv3 * I_{{rv3 = V_{rv3² / Rv3} \\
      Current direction: incoming (I_{{rv3)} \\
} \\
    Rw3 (R): 6→10} \\
      Voltage: V_{rw3 = Vn6 - Vn10} \\
      Current (Ohm's law): I_{{rw3 = V_{rw3 / Rw3} \\
      Power: P_{rw3 = V_{rw3 * I_{{rw3 = V_{rw3² / Rw3} \\
      Current direction: incoming (I_{{rw3)} \\
} \\
  Kirchhoff's Current Law (KCL):} \\
    +I_{{cu1 +I_{{ru3 +I_{{cv1 +I_{{cw1 +I_{{rv3 +I_{{rw3 = 0} \\
    Physical meaning: ∑I_{{incoming = ∑I_{{outgoing} \\
} \\
Node 11 (Vn11):} \\
  Connected components and their equations:} \\
    Cu1 (C): 11→10} \\
      Voltage: V_{cu1 = Vn11 - Vn10} \\
      Current-voltage relation: I_{{cu1 = Cu1 * d(V_{cu1)/dt} \\
      Voltage (continuous): d(V_{cu1)/dt = I_{{cu1 / Cu1} \\
      Voltage (discrete): V_{cu1 = V_{cu1_{old + (I_{{cu1 * dt) / Cu1} \\
      Energy: E_{cu1 = (1/2) * Cu1 * V_{cu1²} \\
      Current direction: outgoing (I_{{cu1)} \\
} \\
    Ru2 (R): 3→11} \\
      Voltage: V_{ru2 = Vn3 - Vn11} \\
      Current (Ohm's law): I_{{ru2 = V_{ru2 / Ru2} \\
      Power: P_{ru2 = V_{ru2 * I_{{ru2 = V_{ru2² / Ru2} \\
      Current direction: incoming (I_{{ru2)} \\
} \\
  Kirchhoff's Current Law (KCL):} \\
    -I_{{cu1 +I_{{ru2 = 0} \\
    Physical meaning: ∑I_{{incoming = ∑I_{{outgoing} \\
} \\
Node 12 (Vn12):} \\
  Connected components and their equations:} \\
    Cv1 (C): 12→10} \\
      Voltage: V_{cv1 = Vn12 - Vn10} \\
      Current-voltage relation: I_{{cv1 = Cv1 * d(V_{cv1)/dt} \\
      Voltage (continuous): d(V_{cv1)/dt = I_{{cv1 / Cv1} \\
      Voltage (discrete): V_{cv1 = V_{cv1_{old + (I_{{cv1 * dt) / Cv1} \\
      Energy: E_{cv1 = (1/2) * Cv1 * V_{cv1²} \\
      Current direction: outgoing (I_{{cv1)} \\
} \\
    Rv2 (R): 9→12} \\
      Voltage: V_{rv2 = Vn9 - Vn12} \\
      Current (Ohm's law): I_{{rv2 = V_{rv2 / Rv2} \\
      Power: P_{rv2 = V_{rv2 * I_{{rv2 = V_{rv2² / Rv2} \\
      Current direction: incoming (I_{{rv2)} \\
} \\
  Kirchhoff's Current Law (KCL):} \\
    -I_{{cv1 +I_{{rv2 = 0} \\
    Physical meaning: ∑I_{{incoming = ∑I_{{outgoing} \\
} \\
Node 13 (Vn13):} \\
  Connected components and their equations:} \\
    Cw1 (C): 13→10} \\
      Voltage: V_{cw1 = Vn13 - Vn10} \\
      Current-voltage relation: I_{{cw1 = Cw1 * d(V_{cw1)/dt} \\
      Voltage (continuous): d(V_{cw1)/dt = I_{{cw1 / Cw1} \\
      Voltage (discrete): V_{cw1 = V_{cw1_{old + (I_{{cw1 * dt) / Cw1} \\
      Energy: E_{cw1 = (1/2) * Cw1 * V_{cw1²} \\
      Current direction: outgoing (I_{{cw1)} \\
} \\
    Rw2 (R): 6→13} \\
      Voltage: V_{rw2 = Vn6 - Vn13} \\
      Current (Ohm's law): I_{{rw2 = V_{rw2 / Rw2} \\
      Power: P_{rw2 = V_{rw2 * I_{{rw2 = V_{rw2² / Rw2} \\
      Current direction: incoming (I_{{rw2)} \\
} \\
  Kirchhoff's Current Law (KCL):} \\
    -I_{{cw1 +I_{{rw2 = 0} \\
    Physical meaning: ∑I_{{incoming = ∑I_{{outgoing} \\
} \\
\end{align}
\end{document}