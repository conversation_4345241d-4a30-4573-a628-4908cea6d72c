\documentclass{article}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{array}
\begin{document}
\title{Advanced Circuit Equations}
\author{Source file: input/ACF3.cct}
\maketitle
\section{Component Equations}
\subsection{Capacitor Cu3}
\begin{align}
V_{{cu3 = Vn1 - Vn3} \\
I_{{cu3 = Cu1 * ((Vn1 - Vn3) - V_{cu3_{old) / dt}
\end{align}
\subsection{Capacitor Cv3}
\begin{align}
V_{{cv3 = Vn2 - Vn3} \\
I_{{cv3 = Cv1 * ((Vn2 - Vn3) - V_{cv3_{old) / dt}
\end{align}
\subsection{Capacitor Cw3}
\begin{align}
V_{{cw3 = Vn4 - Vn3} \\
I_{{cw3 = Cw1 * ((Vn4 - Vn3) - V_{cw3_{old) / dt}
\end{align}
\section{<PERSON><PERSON><PERSON>'s Current Law}
\begin{align}
Node 3 (GROUND) (Vn3):} \\
  Connected components and their equations:} \\
    Cu3 (C): 1→3} \\
      Voltage: V_{cu3 = Vn1 - Vn3} \\
      Current-voltage relation: I_{{cu3 = Cu1 * d(V_{cu3)/dt} \\
      Voltage (continuous): d(V_{cu3)/dt = I_{{cu3 / Cu1} \\
      Voltage (discrete): V_{cu3 = V_{cu3_{old + (I_{{cu3 * dt) / Cu1} \\
      Energy: E_{cu3 = (1/2) * Cu1 * V_{cu3²} \\
      Current direction: incoming (I_{{cu3)} \\
} \\
    Cv3 (C): 2→3} \\
      Voltage: V_{cv3 = Vn2 - Vn3} \\
      Current-voltage relation: I_{{cv3 = Cv1 * d(V_{cv3)/dt} \\
      Voltage (continuous): d(V_{cv3)/dt = I_{{cv3 / Cv1} \\
      Voltage (discrete): V_{cv3 = V_{cv3_{old + (I_{{cv3 * dt) / Cv1} \\
      Energy: E_{cv3 = (1/2) * Cv1 * V_{cv3²} \\
      Current direction: incoming (I_{{cv3)} \\
} \\
    Cw3 (C): 4→3} \\
      Voltage: V_{cw3 = Vn4 - Vn3} \\
      Current-voltage relation: I_{{cw3 = Cw1 * d(V_{cw3)/dt} \\
      Voltage (continuous): d(V_{cw3)/dt = I_{{cw3 / Cw1} \\
      Voltage (discrete): V_{cw3 = V_{cw3_{old + (I_{{cw3 * dt) / Cw1} \\
      Energy: E_{cw3 = (1/2) * Cw1 * V_{cw3²} \\
      Current direction: incoming (I_{{cw3)} \\
} \\
  Kirchhoff's Current Law (KCL):} \\
    +I_{{cu3 +I_{{cv3 +I_{{cw3 = 0} \\
    Physical meaning: ∑I_{{incoming = ∑I_{{outgoing} \\
} \\
\end{align}
\end{document}