=== SPECIAL CIRCUIT ANALYSIS ===
Source file: input/test_circuit_001.cct

Extended <PERSON><PERSON>'s Voltage Law (KVL) Analysis:
For each independent loop in the circuit:

Loop 1: Ru1 → Rv1 → Rw1
  KVL: V_ru1 + V_rv1 + V_rw1 = 0

Loop Current Analysis:
Independent current relationships:

  Node 2: +I_ru1 -I_lu1 = 0
  Node 3: +I_lu1 -I_ru2 -I_ru3 = 0
  Node 5: +I_rw1 -I_lw1 = 0
  Node 6: +I_lw1 -I_rw2 -I_rw3 = 0
  Node 8: +I_rv1 -I_lv1 = 0
  Node 9: +I_lv1 -I_rv2 -I_rv3 = 0
  Node 11: -I_cu1 +I_ru2 = 0
  Node 12: -I_cv1 +I_rv2 = 0
  Node 13: -I_cw1 +I_rw2 = 0

Energy Analysis:
Total system energy:

Magnetic energy (inductors):
  E_lu1 = (1/2) * Lu1 * I_lu1²
  E_lv1 = (1/2) * Lv1 * I_lv1²
  E_lw1 = (1/2) * Lw1 * I_lw1²
  Total magnetic energy: E_L = E_lu1 + E_lv1 + E_lw1

Electric energy (capacitors):
  E_cu1 = (1/2) * Cu1 * V_cu1²
  E_cv1 = (1/2) * Cv1 * V_cv1²
  E_cw1 = (1/2) * Cw1 * V_cw1²
  Total electric energy: E_C = E_cu1 + E_cv1 + E_cw1

Total system energy: E_total = E_L + E_C

System Matrix Analysis:

State vector:
x = [I_lu1, I_lv1, I_lw1, V_cu1, V_cv1, V_cw1]ᵀ

State-space representation:
dx/dt = A*x + B*u
y = C*x + D*u

Where:
  x = state vector (inductor currents, capacitor voltages)
  u = input vector (voltage sources, current sources)
  y = output vector (measured variables)
  A = system matrix (circuit topology dependent)
  B = input matrix
  C = output matrix
  D = feedthrough matrix

ACF-Type Circuit Specialized Analysis:

Kirchhoff's Voltage Law (KVL) for each phase:
VRu1 + VLu1 + Vcu2 + VLn1 = Vu1 - Vn1
VRv1 + VLv1 + Vcv2 + VLn1 = Vv1 - Vn1
VRw1 + VLw1 + Vcw2 + VLn1 = Vw1 - Vn1

Inductor current relationships:
ILu1 + ILv1 + ILw1 = ILn1

Inductor voltage equations:
VLu1 + VLn1 = Lu1 * d(ILu1)/dt + Ln1 * d(ILn1)/dt
VLv1 + VLn1 = Lv1 * d(ILv1)/dt + Ln1 * d(ILn1)/dt
VLw1 + VLn1 = Lw1 * d(ILw1)/dt + Ln1 * d(ILn1)/dt

Substituting current relationship:
VLu1 + VLn1 = Lu1 * d(ILu1)/dt + Ln1 * d(ILu1 + ILv1 + ILw1)/dt
VLv1 + VLn1 = Lv1 * d(ILv1)/dt + Ln1 * d(ILu1 + ILv1 + ILw1)/dt
VLw1 + VLn1 = Lw1 * d(ILw1)/dt + Ln1 * d(ILu1 + ILv1 + ILw1)/dt

Assuming all inductances are equal (L1):
VLu1 + VLn1 = 2*L1 * d(ILu1)/dt + L1 * d(ILv1)/dt + L1 * d(ILw1)/dt
VLv1 + VLn1 = L1 * d(ILu1)/dt + 2*L1 * d(ILv1)/dt + L1 * d(ILw1)/dt
VLw1 + VLn1 = L1 * d(ILu1)/dt + L1 * d(ILv1)/dt + 2*L1 * d(ILw1)/dt

Matrix form representation:
⎡VLu1 + VLn1⎤   ⎡2*L1  L1   L1 ⎤ ⎡d(ILu1)/dt⎤
⎢VLv1 + VLn1⎥ = ⎢ L1  2*L1  L1 ⎥ ⎢d(ILv1)/dt⎥
⎣VLw1 + VLn1⎦   ⎣ L1   L1  2*L1⎦ ⎣d(ILw1)/dt⎦

Inverse matrix solution:
⎡d(ILu1)/dt⎤     1   ⎡ 3  -1  -1⎤ ⎡VLu1 + VLn1⎤
⎢d(ILv1)/dt⎥ = ---- ⎢-1   3  -1⎥ ⎢VLv1 + VLn1⎥
⎣d(ILw1)/dt⎦   4*L1 ⎣-1  -1   3⎦ ⎣VLw1 + VLn1⎦

Voltage substitutions from KVL:
VLu1 + VLn1 = Vu1 - Vn1 - Vcu2 - VRu1
VLv1 + VLn1 = Vv1 - Vn1 - Vcv2 - VRv1
VLw1 + VLn1 = Vw1 - Vn1 - Vcw2 - VRw1

Final current differential equations:
⎡d(ILu1)/dt⎤     1   ⎡ 3  -1  -1⎤ ⎡Vu1 - Vn1 - Vcu2 - VRu1⎤
⎢d(ILv1)/dt⎥ = ---- ⎢-1   3  -1⎥ ⎢Vv1 - Vn1 - Vcv2 - VRv1⎥
⎣d(ILw1)/dt⎦   4*L1 ⎣-1  -1   3⎦ ⎣Vw1 - Vn1 - Vcw2 - VRw1⎦

Capacitor current relationships (KCL):
ICu1 = Icu1 + Iru3 + Icv1 + Icw1 + Irv3 + Irw3
ICv1 = Irv2
ICw1 = Irw2

Capacitor voltage equations:
Vcu2 = (1/Cu2) * ∫ICu2 dt
Vcv2 = (1/Cv2) * ∫ICv2 dt
Vcw2 = (1/Cw2) * ∫ICw2 dt
