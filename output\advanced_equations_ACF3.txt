=== ADVANCED CIRCUIT EQUATIONS ===
Source file: input/ACF3.cct

Capacitor Cu3 (C = Cu1):
  Nodes: 1 - 3
  Voltage: V_cu3 = Vn1 - Vn3
  Current (continuous): I_cu3 = Cu1 * d(V_cu3)/dt
  Current (discrete): I_cu3 = Cu1 * ((Vn1 - Vn3) - V_cu3_old) / dt
  Integration: V_cu3 = (1/Cu1) * ∫I_cu3 dt

Capacitor Cv3 (C = Cv1):
  Nodes: 2 - 3
  Voltage: V_cv3 = Vn2 - Vn3
  Current (continuous): I_cv3 = Cv1 * d(V_cv3)/dt
  Current (discrete): I_cv3 = Cv1 * ((Vn2 - Vn3) - V_cv3_old) / dt
  Integration: V_cv3 = (1/Cv1) * ∫I_cv3 dt

Capacitor Cw3 (C = Cw1):
  Nodes: 4 - 3
  Voltage: V_cw3 = Vn4 - Vn3
  Current (continuous): I_cw3 = Cw1 * d(V_cw3)/dt
  Current (discrete): I_cw3 = Cw1 * ((Vn4 - Vn3) - V_cw3_old) / dt
  Integration: V_cw3 = (1/Cw1) * ∫I_cw3 dt

=== KIRCHHOFF'S CURRENT LAW (KCL) ===
Complete nodal analysis with component equations:
Each node shows:
  - All connected components and their types
  - Voltage equations for each component
  - Current-voltage relationships (Ohm's law, L/C dynamics)
  - Energy equations for reactive components
  - Current direction and KCL equation

Node 3 (GROUND) (Vn3):
  Connected components and their equations:
    Cu3 (C): 1→3
      Voltage: V_cu3 = Vn1 - Vn3
      Current-voltage relation: I_cu3 = Cu1 * d(V_cu3)/dt
      Voltage (continuous): d(V_cu3)/dt = I_cu3 / Cu1
      Voltage (discrete): V_cu3 = V_cu3_old + (I_cu3 * dt) / Cu1
      Energy: E_cu3 = (1/2) * Cu1 * V_cu3²
      Current direction: incoming (I_cu3)

    Cv3 (C): 2→3
      Voltage: V_cv3 = Vn2 - Vn3
      Current-voltage relation: I_cv3 = Cv1 * d(V_cv3)/dt
      Voltage (continuous): d(V_cv3)/dt = I_cv3 / Cv1
      Voltage (discrete): V_cv3 = V_cv3_old + (I_cv3 * dt) / Cv1
      Energy: E_cv3 = (1/2) * Cv1 * V_cv3²
      Current direction: incoming (I_cv3)

    Cw3 (C): 4→3
      Voltage: V_cw3 = Vn4 - Vn3
      Current-voltage relation: I_cw3 = Cw1 * d(V_cw3)/dt
      Voltage (continuous): d(V_cw3)/dt = I_cw3 / Cw1
      Voltage (discrete): V_cw3 = V_cw3_old + (I_cw3 * dt) / Cw1
      Energy: E_cw3 = (1/2) * Cw1 * V_cw3²
      Current direction: incoming (I_cw3)

  Kirchhoff's Current Law (KCL):
    +I_cu3 +I_cv3 +I_cw3 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

