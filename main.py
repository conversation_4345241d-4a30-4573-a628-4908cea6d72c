"""
Main program for Circuit Analysis and Analog Model Generation
Processes .cct files and generates mathematical equations and analog models
"""

import os
import argparse
from pathlib import Path
from circuit_parser import CircuitParser
from equation_generator import EquationGenerator

def create_output_directory():
    """Create output directory if it doesn't exist"""
    if not os.path.exists("output"):
        os.makedirs("output")

def process_circuit_file(input_file: str, generate_code: bool = False):
    """Process a circuit file and generate outputs"""
    
    print(f"Processing circuit file: {input_file}")
    print("="*50)
    
    # Parse the circuit file
    parser = CircuitParser()
    try:
        parser.parse_file(input_file)
        print("✓ Circuit file parsed successfully")
    except Exception as e:
        print(f"✗ Error parsing circuit file: {e}")
        return
    
    # Display circuit information
    parser.print_circuit_info()
    print("\n" + "="*50 + "\n")
    
    # Generate equations
    eq_generator = EquationGenerator(parser, input_file)
    try:
        equations = eq_generator.generate_capacitor_equations()
        print("✓ Equations generated successfully")
    except Exception as e:
        print(f"✗ Error generating equations: {e}")
        return
    
    # Create output directory
    create_output_directory()

    # Extract filename without extension for output file naming
    input_path = Path(input_file)
    file_stem = input_path.stem  # Gets filename without extension

    # Generate text equations
    text_equations = eq_generator.generate_text_equations()
    print(text_equations)

    # Save text equations to file with filename included
    output_txt_file = f"output/equations_{file_stem}.txt"
    with open(output_txt_file, "w", encoding="utf-8") as f:
        f.write(text_equations)
    print(f"✓ Text equations saved to {output_txt_file}")

    # Generate LaTeX equations
    latex_equations = eq_generator.generate_latex_equations(input_file)
    output_tex_file = f"output/equations_{file_stem}.tex"
    with open(output_tex_file, "w", encoding="utf-8") as f:
        f.write(latex_equations)
    print(f"✓ LaTeX equations saved to {output_tex_file}")
    
    # Python code generation (disabled by default)
    if generate_code:
        python_code = eq_generator.generate_python_code()
        with open("output/simulation_code.py", "w", encoding="utf-8") as f:
            f.write(python_code)
        print("✓ Python simulation code saved to output/simulation_code.py")

        print("\nGenerated Python Code:")
        print("-" * 30)
        print(python_code)
    else:
        print("✓ Focus on equation formulation only")
    


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Circuit Analysis and Analog Model Generator")
    parser.add_argument("input_file", nargs="?", default="input/ACF1.cct", 
                       help="Input .cct circuit file (default: input/ACF1.cct)")
    parser.add_argument("--with-code", action="store_true",
                       help="Enable Python code generation (disabled by default)")
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' not found")
        return
    
    # Process the circuit file
    process_circuit_file(
        args.input_file,
        generate_code=args.with_code
    )
    
    print("\n" + "="*50)
    print("Processing completed!")
    # Get filename for output messages
    input_path = Path(args.input_file)
    file_stem = input_path.stem

    print("Output files generated in 'output/' directory:")
    print(f"  - equations_{file_stem}.txt (Text format equations)")
    print(f"  - equations_{file_stem}.tex (LaTeX format equations)")
    if args.with_code:
        print("  - simulation_code.py (Python simulation code)")

    print("\nNote: Python code generation is disabled by default.")
    print("      Focus is on mathematical equation formulation only.")
    print("\nFor advanced circuit analysis with R, L, C components, use:")
    print("  python advanced_main.py [circuit_file]")

if __name__ == "__main__":
    main()
