=== ADVANCED CIRCUIT EQUATIONS ===
Source file: input/test_circuit.cct

Resistor RL1 (R = RL1):
  Nodes: 1 - 2
  Voltage: V_rl1 = Vn1 - Vn2
  Current (continuous): I_rl1 = V_rl1 / RL1
  Current (discrete): I_rl1 = (Vn1 - Vn2) / RL1

Inductor L1 (I = L1):
  Nodes: 3 - 1
  Voltage: V_l1 = Vn3 - Vn1
  Current (continuous): V_l1 = L1 * d(I_l1)/dt
  Current (discrete): I_l1 = I_l1_old + ((Vn3 - Vn1) * dt) / L1
  Integration: I_l1 = (1/L1) * ∫V_l1 dt

Capacitor C1 (C = C1):
  Nodes: 1 - 2
  Voltage: V_c1 = Vn1 - Vn2
  Current (continuous): I_c1 = C1 * d(V_c1)/dt
  Current (discrete): I_c1 = C1 * ((Vn1 - Vn2) - V_c1_old) / dt
  Integration: V_c1 = (1/C1) * ∫I_c1 dt

=== KIRCHHOFF'S CURRENT LAW (KCL) ===
Complete nodal analysis with component equations:
Each node shows:
  - All connected components and their types
  - Voltage equations for each component
  - Current-voltage relationships (Ohm's law, L/C dynamics)
  - Energy equations for reactive components
  - Current direction and KCL equation

Node 1 (GROUND) (Vn1):
  Connected components and their equations:
    C1 (C): 1→2
      Voltage: V_c1 = Vn1 - Vn2
      Current-voltage relation: I_c1 = C1 * d(V_c1)/dt
      Voltage (continuous): d(V_c1)/dt = I_c1 / C1
      Voltage (discrete): V_c1 = V_c1_old + (I_c1 * dt) / C1
      Energy: E_c1 = (1/2) * C1 * V_c1²
      Current direction: outgoing (I_c1)

    L1 (L): 3→1
      Voltage: V_l1 = Vn3 - Vn1
      Voltage-current relation: V_l1 = L1 * d(I_l1)/dt
      Current (continuous): d(I_l1)/dt = V_l1 / L1
      Current (discrete): I_l1 = I_l1_old + (V_l1 * dt) / L1
      Energy: E_l1 = (1/2) * L1 * I_l1²
      Current direction: incoming (I_l1)

    RL1 (R): 1→2
      Voltage: V_rl1 = Vn1 - Vn2
      Current (Ohm's law): I_rl1 = V_rl1 / RL1
      Power: P_rl1 = V_rl1 * I_rl1 = V_rl1² / RL1
      Current direction: outgoing (I_rl1)

  Kirchhoff's Current Law (KCL):
    -I_c1 +I_l1 -I_rl1 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 2 (Vn2):
  Connected components and their equations:
    C1 (C): 1→2
      Voltage: V_c1 = Vn1 - Vn2
      Current-voltage relation: I_c1 = C1 * d(V_c1)/dt
      Voltage (continuous): d(V_c1)/dt = I_c1 / C1
      Voltage (discrete): V_c1 = V_c1_old + (I_c1 * dt) / C1
      Energy: E_c1 = (1/2) * C1 * V_c1²
      Current direction: incoming (I_c1)

    RL1 (R): 1→2
      Voltage: V_rl1 = Vn1 - Vn2
      Current (Ohm's law): I_rl1 = V_rl1 / RL1
      Power: P_rl1 = V_rl1 * I_rl1 = V_rl1² / RL1
      Current direction: incoming (I_rl1)

  Kirchhoff's Current Law (KCL):
    +I_c1 +I_rl1 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

