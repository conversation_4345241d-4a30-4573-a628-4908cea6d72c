=== ADVANCED CIRCUIT EQUATIONS ===
Source file: input/test_circuit.cct

Resistor R1 (R = R1):
  Nodes: 1 - 4
  Voltage: V_r1 = Vn1 - Vn4
  Current (continuous): I_r1 = V_r1 / R1
  Current (discrete): I_r1 = (Vn1 - Vn4) / R1

Inductor L1 (I = L1):
  Nodes: 3 - 1
  Voltage: V_l1 = Vn3 - Vn1
  Current (continuous): V_l1 = L1 * d(I_l1)/dt
  Current (discrete): I_l1 = I_l1_old + ((Vn3 - Vn1) * dt) / L1
  Integration: I_l1 = (1/L1) * ∫V_l1 dt

Inductor L2 (I = L2):
  Nodes: 4 - 2
  Voltage: V_l2 = Vn4 - Vn2
  Current (continuous): V_l2 = L2 * d(I_l2)/dt
  Current (discrete): I_l2 = I_l2_old + ((Vn4 - Vn2) * dt) / L2
  Integration: I_l2 = (1/L2) * ∫V_l2 dt

Capacitor C1 (C = C1):
  Nodes: 1 - 2
  Voltage: V_c1 = Vn1 - Vn2
  Current (continuous): I_c1 = C1 * d(V_c1)/dt
  Current (discrete): I_c1 = C1 * ((Vn1 - Vn2) - V_c1_old) / dt
  Integration: V_c1 = (1/C1) * ∫I_c1 dt

=== KIRCHHOFF'S CURRENT LAW (KCL) ===
Complete nodal analysis with component equations:
Each node shows:
  - All connected components and their types
  - Voltage equations for each component
  - Current-voltage relationships (Ohm's law, L/C dynamics)
  - Energy equations for reactive components
  - Current direction and KCL equation

Node 1 (GROUND) (Vn1):
  Connected components and their equations:
    C1 (C): 1→2
      Voltage: V_c1 = Vn1 - Vn2
      Current-voltage relation: I_c1 = C1 * d(V_c1)/dt
      Voltage (continuous): d(V_c1)/dt = I_c1 / C1
      Voltage (discrete): V_c1 = V_c1_old + (I_c1 * dt) / C1
      Energy: E_c1 = (1/2) * C1 * V_c1²
      Current direction: outgoing (I_c1)

    L1 (L): 3→1
      Voltage: V_l1 = Vn3 - Vn1
      Voltage-current relation: V_l1 = L1 * d(I_l1)/dt
      Current (continuous): d(I_l1)/dt = V_l1 / L1
      Current (discrete): I_l1 = I_l1_old + (V_l1 * dt) / L1
      Energy: E_l1 = (1/2) * L1 * I_l1²
      Current direction: incoming (I_l1)

    R1 (R): 1→4
      Voltage: V_r1 = Vn1 - Vn4
      Current (Ohm's law): I_r1 = V_r1 / R1
      Power: P_r1 = V_r1 * I_r1 = V_r1² / R1
      Current direction: outgoing (I_r1)

  Kirchhoff's Current Law (KCL):
    -I_c1 +I_l1 -I_r1 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 2 (Vn2):
  Connected components and their equations:
    C1 (C): 1→2
      Voltage: V_c1 = Vn1 - Vn2
      Current-voltage relation: I_c1 = C1 * d(V_c1)/dt
      Voltage (continuous): d(V_c1)/dt = I_c1 / C1
      Voltage (discrete): V_c1 = V_c1_old + (I_c1 * dt) / C1
      Energy: E_c1 = (1/2) * C1 * V_c1²
      Current direction: incoming (I_c1)

    L2 (L): 4→2
      Voltage: V_l2 = Vn4 - Vn2
      Voltage-current relation: V_l2 = L2 * d(I_l2)/dt
      Current (continuous): d(I_l2)/dt = V_l2 / L2
      Current (discrete): I_l2 = I_l2_old + (V_l2 * dt) / L2
      Energy: E_l2 = (1/2) * L2 * I_l2²
      Current direction: incoming (I_l2)

  Kirchhoff's Current Law (KCL):
    +I_c1 +I_l2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

Node 4 (Vn4):
  Connected components and their equations:
    R1 (R): 1→4
      Voltage: V_r1 = Vn1 - Vn4
      Current (Ohm's law): I_r1 = V_r1 / R1
      Power: P_r1 = V_r1 * I_r1 = V_r1² / R1
      Current direction: incoming (I_r1)

    L2 (L): 4→2
      Voltage: V_l2 = Vn4 - Vn2
      Voltage-current relation: V_l2 = L2 * d(I_l2)/dt
      Current (continuous): d(I_l2)/dt = V_l2 / L2
      Current (discrete): I_l2 = I_l2_old + (V_l2 * dt) / L2
      Energy: E_l2 = (1/2) * L2 * I_l2²
      Current direction: outgoing (I_l2)

  Kirchhoff's Current Law (KCL):
    +I_r1 -I_l2 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

