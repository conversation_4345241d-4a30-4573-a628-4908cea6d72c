"""
Advanced Equation Generator for Complex Circuit Analysis
Generates comprehensive equations for R, L, C circuits using KVL and KCL
"""

import numpy as np
from typing import Dict, <PERSON>, Tuple, Optional
from dataclasses import dataclass
from advanced_circuit_parser import AdvancedCircuitParser, CircuitComponent, ComponentType

@dataclass
class ComponentEquation:
    """Represents equations for a circuit component"""
    name: str
    component_type: str
    value: str
    node1: int
    node2: int
    voltage_equation: str
    current_equation: str
    current_equation_discrete: str
    voltage_integration: Optional[str] = None
    additional_equations: List[str] = None

class AdvancedEquationGenerator:
    """Generates mathematical equations for complex circuit analysis"""

    def __init__(self, parser: AdvancedCircuitParser, input_filename: str = ""):
        self.parser = parser
        self.input_filename = input_filename
        self.component_equations: Dict[str, ComponentEquation] = {}
        self.kvl_equations: List[str] = []
        self.kcl_equations: List[str] = []
        self.matrix_equations: List[str] = []
    
    def generate_all_equations(self) -> Dict[str, ComponentEquation]:
        """Generate equations for all components in the circuit"""
        # Generate individual component equations
        self._generate_resistor_equations()
        self._generate_inductor_equations()
        self._generate_capacitor_equations()
        
        # Generate system equations
        self._generate_kvl_equations()
        self._generate_kcl_equations()
        self._generate_matrix_equations()
        
        return self.component_equations
    
    def _generate_resistor_equations(self) -> None:
        """Generate equations for resistors"""
        resistors = self.parser.get_resistors()
        
        for res_name, res_component in resistors.items():
            equation = self._generate_resistor_equation(res_component)
            self.component_equations[res_name] = equation
    
    def _generate_resistor_equation(self, resistor: CircuitComponent) -> ComponentEquation:
        """Generate equations for a single resistor"""
        res_name = resistor.name
        resistance = resistor.value
        node1 = resistor.node1
        node2 = resistor.node2
        
        # Generate voltage names
        v_node1 = self._get_voltage_name(node1)
        v_node2 = self._get_voltage_name(node2)
        v_res = f"V_{res_name.lower()}"
        i_res = f"I_{res_name.lower()}"
        
        # Voltage equation: V_res = V_node1 - V_node2
        voltage_equation = f"{v_res} = {v_node1} - {v_node2}"
        
        # Current equation: I_res = V_res / R (Ohm's law)
        current_equation = f"{i_res} = {v_res} / {resistance}"
        
        # Discrete current equation (same for resistor)
        current_equation_discrete = f"{i_res} = ({v_node1} - {v_node2}) / {resistance}"
        
        return ComponentEquation(
            name=res_name,
            component_type="Resistor",
            value=resistance,
            node1=node1,
            node2=node2,
            voltage_equation=voltage_equation,
            current_equation=current_equation,
            current_equation_discrete=current_equation_discrete
        )
    
    def _generate_inductor_equations(self) -> None:
        """Generate equations for inductors"""
        inductors = self.parser.get_inductors()
        
        for ind_name, ind_component in inductors.items():
            equation = self._generate_inductor_equation(ind_component)
            self.component_equations[ind_name] = equation
    
    def _generate_inductor_equation(self, inductor: CircuitComponent) -> ComponentEquation:
        """Generate equations for a single inductor"""
        ind_name = inductor.name
        inductance = inductor.value
        node1 = inductor.node1
        node2 = inductor.node2
        
        # Generate voltage names
        v_node1 = self._get_voltage_name(node1)
        v_node2 = self._get_voltage_name(node2)
        v_ind = f"V_{ind_name.lower()}"
        i_ind = f"I_{ind_name.lower()}"
        
        # Voltage equation: V_ind = V_node1 - V_node2
        voltage_equation = f"{v_ind} = {v_node1} - {v_node2}"
        
        # Current equation: V_ind = L * d(I_ind)/dt
        current_equation = f"{v_ind} = {inductance} * d({i_ind})/dt"
        
        # Discrete current equation: I_ind = I_ind_old + (V_ind * dt) / L
        current_equation_discrete = (
            f"{i_ind} = {i_ind}_old + "
            f"(({v_node1} - {v_node2}) * dt) / {inductance}"
        )
        
        # Current integration equation: I_ind = (1/L) * ∫V_ind dt
        voltage_integration = f"{i_ind} = (1/{inductance}) * ∫{v_ind} dt"
        
        return ComponentEquation(
            name=ind_name,
            component_type="Inductor",
            value=inductance,
            node1=node1,
            node2=node2,
            voltage_equation=voltage_equation,
            current_equation=current_equation,
            current_equation_discrete=current_equation_discrete,
            voltage_integration=voltage_integration
        )
    
    def _generate_capacitor_equations(self) -> None:
        """Generate equations for capacitors"""
        capacitors = self.parser.get_capacitors()
        
        for cap_name, cap_component in capacitors.items():
            equation = self._generate_capacitor_equation(cap_component)
            self.component_equations[cap_name] = equation
    
    def _generate_capacitor_equation(self, capacitor: CircuitComponent) -> ComponentEquation:
        """Generate equations for a single capacitor"""
        cap_name = capacitor.name
        capacitance = capacitor.value
        node1 = capacitor.node1
        node2 = capacitor.node2
        
        # Generate voltage names
        v_node1 = self._get_voltage_name(node1)
        v_node2 = self._get_voltage_name(node2)
        v_cap = f"V_{cap_name.lower()}"
        i_cap = f"I_{cap_name.lower()}"
        
        # Voltage equation: V_cap = V_node1 - V_node2
        voltage_equation = f"{v_cap} = {v_node1} - {v_node2}"
        
        # Current equation: I_cap = C * d(V_cap)/dt
        current_equation = f"{i_cap} = {capacitance} * d({v_cap})/dt"
        
        # Discrete current equation: I_cap = C * (V_cap - V_cap_old) / dt
        current_equation_discrete = (
            f"{i_cap} = {capacitance} * "
            f"(({v_node1} - {v_node2}) - {v_cap}_old) / dt"
        )
        
        # Voltage integration equation: V_cap = (1/C) * ∫I_cap dt
        voltage_integration = f"{v_cap} = (1/{capacitance}) * ∫{i_cap} dt"
        
        return ComponentEquation(
            name=cap_name,
            component_type="Capacitor",
            value=capacitance,
            node1=node1,
            node2=node2,
            voltage_equation=voltage_equation,
            current_equation=current_equation,
            current_equation_discrete=current_equation_discrete,
            voltage_integration=voltage_integration
        )
    
    def _generate_kvl_equations(self) -> None:
        """Generate Kirchhoff's Voltage Law equations"""
        # For each loop in the circuit, sum of voltages = 0
        # This is a simplified implementation
        self.kvl_equations = []
        
        # Example: For a simple series circuit
        # V_source - V_R - V_L - V_C = 0
        
    def _generate_kcl_equations(self) -> None:
        """Generate Kirchhoff's Current Law equations"""
        # For each node, sum of currents = 0
        self.kcl_equations = []

        for node_id, node in self.parser.nodes.items():
            # Skip ground nodes only if they have less than 2 connections
            # Ground nodes with multiple connections still need KCL equations
            connected_components = self.parser.get_connected_components(node_id)
            if node.is_ground and len(connected_components) <= 1:
                continue

            if len(connected_components) > 1:
                # Generate detailed KCL equation with component information
                kcl_info = self._generate_detailed_kcl_for_node(node_id, connected_components)
                self.kcl_equations.extend(kcl_info)

    def _generate_detailed_kcl_for_node(self, node_id: int, connected_components) -> List[str]:
        """Generate detailed KCL equation for a specific node with complete component analysis"""
        node_info = []

        # Node header
        node_label = f"Node {node_id}"
        if self.parser.nodes[node_id].is_ground:
            node_label += " (GROUND)"

        # Get node voltage name
        node_voltage = self._get_voltage_name(node_id)

        # List connected components with detailed equations
        component_details = []
        current_terms = []

        node_info.append(f"{node_label} ({node_voltage}):")
        node_info.append("  Connected components and their equations:")

        for comp in connected_components:
            current_name = f"I_{comp.name.lower()}"
            voltage_name = f"V_{comp.name.lower()}"
            comp_type = comp.component_type.value if hasattr(comp.component_type, 'value') else str(comp.component_type)

            # Get other node voltage name
            other_node = comp.node2 if comp.node1 == node_id else comp.node1
            other_voltage = self._get_voltage_name(other_node)

            # Determine current direction and description
            if comp.node1 == node_id:
                direction = "outgoing"
                current_terms.append(f"-{current_name}")
                direction_symbol = "→"
                connection_desc = f"{node_id}{direction_symbol}{comp.node2}"
                voltage_equation = f"{voltage_name} = {node_voltage} - {other_voltage}"
            else:
                direction = "incoming"
                current_terms.append(f"+{current_name}")
                direction_symbol = "→"
                connection_desc = f"{comp.node1}{direction_symbol}{node_id}"
                voltage_equation = f"{voltage_name} = {other_voltage} - {node_voltage}"

            # Generate component-specific equations
            component_equations = self._generate_component_equations(comp, voltage_name, current_name)

            component_details.append(f"    {comp.name} ({comp_type}): {connection_desc}")
            component_details.append(f"      Voltage: {voltage_equation}")
            component_details.extend([f"      {eq}" for eq in component_equations])
            component_details.append(f"      Current direction: {direction} ({current_name})")
            component_details.append("")

        # Generate the KCL equation
        kcl_equation = " ".join(current_terms) + " = 0"

        # Add to node info
        node_info.extend(component_details)
        node_info.append(f"  Kirchhoff's Current Law (KCL):")
        node_info.append(f"    {kcl_equation}")
        node_info.append(f"    Physical meaning: ∑I_incoming = ∑I_outgoing")
        node_info.append("")

        return node_info

    def _generate_component_equations(self, component, voltage_name: str, current_name: str) -> List[str]:
        """Generate detailed equations for a specific component"""
        equations = []
        comp_type = component.component_type.value if hasattr(component.component_type, 'value') else str(component.component_type)

        if comp_type == 'R':
            # Resistor equations
            equations.append(f"Current (Ohm's law): {current_name} = {voltage_name} / {component.value}")
            equations.append(f"Power: P_{component.name.lower()} = {voltage_name} * {current_name} = {voltage_name}² / {component.value}")

        elif comp_type == 'L':
            # Inductor equations
            equations.append(f"Voltage-current relation: {voltage_name} = {component.value} * d({current_name})/dt")
            equations.append(f"Current (continuous): d({current_name})/dt = {voltage_name} / {component.value}")
            equations.append(f"Current (discrete): {current_name} = {current_name}_old + ({voltage_name} * dt) / {component.value}")
            equations.append(f"Energy: E_{component.name.lower()} = (1/2) * {component.value} * {current_name}²")

        elif comp_type == 'C':
            # Capacitor equations
            equations.append(f"Current-voltage relation: {current_name} = {component.value} * d({voltage_name})/dt")
            equations.append(f"Voltage (continuous): d({voltage_name})/dt = {current_name} / {component.value}")
            equations.append(f"Voltage (discrete): {voltage_name} = {voltage_name}_old + ({current_name} * dt) / {component.value}")
            equations.append(f"Energy: E_{component.name.lower()} = (1/2) * {component.value} * {voltage_name}²")

        return equations
    
    def _generate_matrix_equations(self) -> None:
        """Generate matrix form equations for system analysis"""
        # This would generate state-space or impedance matrix equations
        # Placeholder for more advanced matrix formulation
        self.matrix_equations = []
    
    def _get_voltage_name(self, node_id: int) -> str:
        """Get voltage name for a node"""
        if node_id in self.parser.nodes:
            return self.parser.nodes[node_id].voltage_name
        else:
            return f"Vn{node_id}"
    
    def generate_text_equations(self) -> str:
        """Generate plain text formatted equations"""
        text_output = []
        text_output.append("=== ADVANCED CIRCUIT EQUATIONS ===")
        if self.input_filename:
            text_output.append(f"Source file: {self.input_filename}")
        text_output.append("")

        # Component equations
        for comp_name, eq in self.component_equations.items():
            text_output.append(f"{eq.component_type} {comp_name} ({eq.component_type[0]} = {eq.value}):")
            text_output.append(f"  Nodes: {eq.node1} - {eq.node2}")
            text_output.append(f"  Voltage: {eq.voltage_equation}")
            text_output.append(f"  Current (continuous): {eq.current_equation}")
            text_output.append(f"  Current (discrete): {eq.current_equation_discrete}")
            if eq.voltage_integration:
                text_output.append(f"  Integration: {eq.voltage_integration}")
            text_output.append("")

        # KCL equations
        if self.kcl_equations:
            text_output.append("=== KIRCHHOFF'S CURRENT LAW (KCL) ===")
            text_output.append("Complete nodal analysis with component equations:")
            text_output.append("Each node shows:")
            text_output.append("  - All connected components and their types")
            text_output.append("  - Voltage equations for each component")
            text_output.append("  - Current-voltage relationships (Ohm's law, L/C dynamics)")
            text_output.append("  - Energy equations for reactive components")
            text_output.append("  - Current direction and KCL equation")
            text_output.append("")
            for kcl_eq in self.kcl_equations:
                text_output.append(kcl_eq)
            text_output.append("")

        # KVL equations
        if self.kvl_equations:
            text_output.append("=== KIRCHHOFF'S VOLTAGE LAW (KVL) ===")
            for kvl_eq in self.kvl_equations:
                text_output.append(f"  {kvl_eq}")
            text_output.append("")

        return "\n".join(text_output)

    def generate_python_code(self) -> str:
        """Generate Python code for numerical computation - DISABLED"""
        return "# Python code generation is disabled. Only equation formulation is provided."



    def generate_acf2_special_equations(self) -> List[str]:
        """Generate generalized special circuit analysis for any circuit"""
        special_equations = []

        # Always generate special analysis for any circuit
        special_equations.append("=== SPECIAL CIRCUIT ANALYSIS ===")
        if self.input_filename:
            special_equations.append(f"Source file: {self.input_filename}")
        special_equations.append("")

        # Generate generalized analysis based on circuit topology
        special_equations.extend(self._generate_generalized_circuit_analysis())

        return special_equations

    def _generate_generalized_circuit_analysis(self) -> List[str]:
        """Generate generalized circuit analysis for any circuit topology"""
        equations = []

        # Analyze circuit topology
        inductors = self.parser.get_inductors()
        capacitors = self.parser.get_capacitors()
        resistors = self.parser.get_resistors()

        # Generate comprehensive KVL analysis
        equations.extend(self._generate_comprehensive_kvl_analysis())

        # Generate loop analysis
        equations.extend(self._generate_loop_analysis())

        # Generate energy analysis
        equations.extend(self._generate_energy_analysis())

        # Generate system matrix analysis if multiple inductors/capacitors exist
        if len(inductors) > 1 or len(capacitors) > 1:
            equations.extend(self._generate_system_matrix_analysis())

        # Check for specific circuit patterns and add specialized analysis
        if self._is_acf_type_circuit(inductors):
            equations.extend(self._generate_acf_specialized_analysis(inductors))

        return equations

    def _generate_comprehensive_kvl_analysis(self) -> List[str]:
        """Generate comprehensive Kirchhoff's Voltage Law analysis"""
        equations = []

        equations.append("Extended Kirchhoff's Voltage Law (KVL) Analysis:")
        equations.append("For each independent loop in the circuit:")
        equations.append("")

        # Find all unique loops by analyzing circuit topology
        loops = self._identify_circuit_loops()

        for i, loop in enumerate(loops, 1):
            equations.append(f"Loop {i}: {' → '.join(loop)}")
            kvl_equation = self._generate_kvl_for_loop(loop)
            equations.append(f"  KVL: {kvl_equation}")
            equations.append("")

        return equations

    def _generate_loop_analysis(self) -> List[str]:
        """Generate loop current analysis"""
        equations = []

        equations.append("Loop Current Analysis:")
        equations.append("Independent current relationships:")
        equations.append("")

        # Analyze current relationships based on circuit topology
        current_relationships = self._analyze_current_relationships()
        for relationship in current_relationships:
            equations.append(f"  {relationship}")

        equations.append("")
        return equations

    def _generate_energy_analysis(self) -> List[str]:
        """Generate energy analysis for reactive components"""
        equations = []

        inductors = self.parser.get_inductors()
        capacitors = self.parser.get_capacitors()

        if inductors or capacitors:
            equations.append("Energy Analysis:")
            equations.append("Total system energy:")
            equations.append("")

            # Magnetic energy from inductors
            if inductors:
                equations.append("Magnetic energy (inductors):")
                total_magnetic = []
                for name, inductor in inductors.items():
                    energy_term = f"(1/2) * {inductor.value} * I_{name.lower()}²"
                    equations.append(f"  E_{name.lower()} = {energy_term}")
                    total_magnetic.append(f"E_{name.lower()}")

                equations.append(f"  Total magnetic energy: E_L = {' + '.join(total_magnetic)}")
                equations.append("")

            # Electric energy from capacitors
            if capacitors:
                equations.append("Electric energy (capacitors):")
                total_electric = []
                for name, capacitor in capacitors.items():
                    energy_term = f"(1/2) * {capacitor.value} * V_{name.lower()}²"
                    equations.append(f"  E_{name.lower()} = {energy_term}")
                    total_electric.append(f"E_{name.lower()}")

                equations.append(f"  Total electric energy: E_C = {' + '.join(total_electric)}")
                equations.append("")

            # Total system energy
            energy_components = []
            if inductors:
                energy_components.append("E_L")
            if capacitors:
                energy_components.append("E_C")

            equations.append(f"Total system energy: E_total = {' + '.join(energy_components)}")
            equations.append("")

        return equations

    def _generate_system_matrix_analysis(self) -> List[str]:
        """Generate system matrix analysis for multi-component circuits"""
        equations = []

        inductors = self.parser.get_inductors()
        capacitors = self.parser.get_capacitors()

        equations.append("System Matrix Analysis:")
        equations.append("")

        # State vector analysis
        state_variables = []
        if inductors:
            for name in inductors.keys():
                state_variables.append(f"I_{name.lower()}")
        if capacitors:
            for name in capacitors.keys():
                state_variables.append(f"V_{name.lower()}")

        if state_variables:
            equations.append("State vector:")
            equations.append(f"x = [{', '.join(state_variables)}]ᵀ")
            equations.append("")

            equations.append("State-space representation:")
            equations.append("dx/dt = A*x + B*u")
            equations.append("y = C*x + D*u")
            equations.append("")
            equations.append("Where:")
            equations.append("  x = state vector (inductor currents, capacitor voltages)")
            equations.append("  u = input vector (voltage sources, current sources)")
            equations.append("  y = output vector (measured variables)")
            equations.append("  A = system matrix (circuit topology dependent)")
            equations.append("  B = input matrix")
            equations.append("  C = output matrix")
            equations.append("  D = feedthrough matrix")
            equations.append("")

        return equations

    def _is_acf_type_circuit(self, inductors: dict) -> bool:
        """Check if circuit follows ACF naming pattern"""
        acf_patterns = ['Lu1', 'Lv1', 'Lw1', 'Ln1']
        return any(pattern in inductors for pattern in acf_patterns)

    def _generate_acf_specialized_analysis(self, inductors: dict) -> List[str]:
        """Generate specialized analysis for ACF-type circuits"""
        equations = []

        equations.append("ACF-Type Circuit Specialized Analysis:")
        equations.append("")

        # Check for primary/secondary structure
        has_secondary = any(name in inductors for name in ['Lu2', 'Lv2', 'Lw2'])

        if has_secondary:
            equations.extend(self._generate_acf2_kawai_analysis(inductors))
        else:
            equations.extend(self._generate_acf2_regular_analysis())

        return equations

    def _identify_circuit_loops(self) -> List[List[str]]:
        """Identify independent loops in the circuit"""
        # Simplified loop identification - in practice, this would use graph theory
        loops = []

        # For now, generate basic loops based on component connections
        # This is a simplified implementation
        components = []
        for comp_dict in [self.parser.get_resistors(), self.parser.get_inductors(), self.parser.get_capacitors()]:
            components.extend(comp_dict.keys())

        if len(components) >= 3:
            # Create a simple loop with first few components
            loops.append(components[:3])

        return loops

    def _generate_kvl_for_loop(self, loop: List[str]) -> str:
        """Generate KVL equation for a specific loop"""
        voltage_terms = []
        for component_name in loop:
            voltage_terms.append(f"V_{component_name.lower()}")

        return f"{' + '.join(voltage_terms)} = 0"

    def _analyze_current_relationships(self) -> List[str]:
        """Analyze current relationships in the circuit"""
        relationships = []

        # Analyze each node for current relationships
        for node_id, node in self.parser.nodes.items():
            if node.is_ground:
                continue

            connected_components = self.parser.get_connected_components(node_id)
            if len(connected_components) > 1:
                # Generate current relationship for this node
                currents = []
                for comp in connected_components:
                    current_name = f"I_{comp.name.lower()}"
                    if comp.node1 == node_id:
                        currents.append(f"-{current_name}")
                    else:
                        currents.append(f"+{current_name}")

                if currents:
                    relationship = f"Node {node_id}: {' '.join(currents)} = 0"
                    relationships.append(relationship)

        return relationships

    def _generate_acf2_regular_analysis(self) -> List[str]:
        """Generate analysis for regular ACF2 circuit (4 inductors)"""
        equations = []

        # KVL equations for each phase
        equations.append("Kirchhoff's Voltage Law (KVL) for each phase:")
        equations.append("VRu1 + VLu1 + Vcu2 + VLn1 = Vu1 - Vn1")
        equations.append("VRv1 + VLv1 + Vcv2 + VLn1 = Vv1 - Vn1")
        equations.append("VRw1 + VLw1 + Vcw2 + VLn1 = Vw1 - Vn1")
        equations.append("")

        # Current relationships
        equations.append("Inductor current relationships:")
        equations.append("ILu1 + ILv1 + ILw1 = ILn1")
        equations.append("")

        # Inductor voltage equations
        equations.append("Inductor voltage equations:")
        equations.append("VLu1 + VLn1 = Lu1 * d(ILu1)/dt + Ln1 * d(ILn1)/dt")
        equations.append("VLv1 + VLn1 = Lv1 * d(ILv1)/dt + Ln1 * d(ILn1)/dt")
        equations.append("VLw1 + VLn1 = Lw1 * d(ILw1)/dt + Ln1 * d(ILn1)/dt")
        equations.append("")

        # Substituting current relationship
        equations.append("Substituting current relationship:")
        equations.append("VLu1 + VLn1 = Lu1 * d(ILu1)/dt + Ln1 * d(ILu1 + ILv1 + ILw1)/dt")
        equations.append("VLv1 + VLn1 = Lv1 * d(ILv1)/dt + Ln1 * d(ILu1 + ILv1 + ILw1)/dt")
        equations.append("VLw1 + VLn1 = Lw1 * d(ILw1)/dt + Ln1 * d(ILu1 + ILv1 + ILw1)/dt")
        equations.append("")

        # Using individual inductance values
        equations.append("Using individual inductance values:")
        equations.append("VLu1 + VLn1 = (Lu1 + Ln1) * d(ILu1)/dt + Ln1 * d(ILv1)/dt + Ln1 * d(ILw1)/dt")
        equations.append("VLv1 + VLn1 = Ln1 * d(ILu1)/dt + (Lv1 + Ln1) * d(ILv1)/dt + Ln1 * d(ILw1)/dt")
        equations.append("VLw1 + VLn1 = Ln1 * d(ILu1)/dt + Ln1 * d(ILv1)/dt + (Lw1 + Ln1) * d(ILw1)/dt")
        equations.append("")

        # Matrix form with individual inductances
        equations.append("Matrix form representation:")
        equations.append("⎡VLu1 + VLn1⎤   ⎡Lu1+Ln1   Ln1     Ln1   ⎤ ⎡d(ILu1)/dt⎤")
        equations.append("⎢VLv1 + VLn1⎥ = ⎢  Ln1   Lv1+Ln1   Ln1   ⎥ ⎢d(ILv1)/dt⎥")
        equations.append("⎣VLw1 + VLn1⎦   ⎣  Ln1     Ln1   Lw1+Ln1⎦ ⎣d(ILw1)/dt⎦")
        equations.append("")

        # General matrix solution (without assuming equal inductances)
        equations.append("General matrix solution:")
        equations.append("⎡d(ILu1)/dt⎤   ⎡Lu1+Ln1   Ln1     Ln1   ⎤⁻¹ ⎡VLu1 + VLn1⎤")
        equations.append("⎢d(ILv1)/dt⎥ = ⎢  Ln1   Lv1+Ln1   Ln1   ⎥   ⎢VLv1 + VLn1⎥")
        equations.append("⎣d(ILw1)/dt⎦   ⎣  Ln1     Ln1   Lw1+Ln1⎦   ⎣VLw1 + VLn1⎦")
        equations.append("")

        # Voltage substitutions
        equations.append("Voltage substitutions from KVL:")
        equations.append("VLu1 + VLn1 = Vu1 - Vn1 - Vcu2 - VRu1")
        equations.append("VLv1 + VLn1 = Vv1 - Vn1 - Vcv2 - VRv1")
        equations.append("VLw1 + VLn1 = Vw1 - Vn1 - Vcw2 - VRw1")
        equations.append("")

        # Final current differential equations with individual inductances
        equations.append("Final current differential equations:")
        equations.append("⎡d(ILu1)/dt⎤   ⎡Lu1+Ln1   Ln1     Ln1   ⎤⁻¹ ⎡Vu1 - Vn1 - Vcu2 - VRu1⎤")
        equations.append("⎢d(ILv1)/dt⎥ = ⎢  Ln1   Lv1+Ln1   Ln1   ⎥   ⎢Vv1 - Vn1 - Vcv2 - VRv1⎥")
        equations.append("⎣d(ILw1)/dt⎦   ⎣  Ln1     Ln1   Lw1+Ln1⎦   ⎣Vw1 - Vn1 - Vcw2 - VRw1⎦")
        equations.append("")

        # Add common analysis sections
        equations.extend(self._add_common_analysis())

        return equations

    def _generate_acf2_kawai_analysis(self, inductors: dict) -> List[str]:
        """Generate analysis for ACF2_kawai circuit (8 inductors)"""
        equations = []

        # Extended KVL equations for ACF2_kawai circuit
        equations.append("Extended Kirchhoff's Voltage Law (KVL) for ACF2_kawai circuit:")
        equations.append("Primary phase equations:")
        equations.append("VRu1 + VLu1 + Vcu2 + VLn1 = Vu1 - Vn1")
        equations.append("VRv1 + VLv1 + Vcv2 + VLn1 = Vv1 - Vn1")
        equations.append("VRw1 + VLw1 + Vcw2 + VLn1 = Vw1 - Vn1")
        equations.append("")
        equations.append("Secondary phase equations:")
        equations.append("Note: Secondary system shares the same voltage sources and capacitors as primary")
        equations.append("VLu2 + VRu2 + VLn2 = VLu1 + VCu2 (from node 2 voltage balance)")
        equations.append("VLv2 + VRv2 + VLn2 = VLv1 + VCv2 (from node 4 voltage balance)")
        equations.append("VLw2 + VRw2 + VLn2 = VLw1 + VCw2 (from node 6 voltage balance)")
        equations.append("")
        equations.append("Substituting primary equations:")
        equations.append("VLu2 + VRu2 + VLn2 = (Vu1 - Vn1 - VRu1 - VLn1) + VCu2")
        equations.append("VLv2 + VRv2 + VLn2 = (Vv1 - Vn1 - VRv1 - VLn1) + VCv2")
        equations.append("VLw2 + VRw2 + VLn2 = (Vw1 - Vn1 - VRw1 - VLn1) + VCw2")
        equations.append("")

        # Current relationships for all inductors
        equations.append("Complete inductor current relationships:")
        equations.append("Primary inductors: ILu1 + ILv1 + ILw1 = ILn1")
        equations.append("Secondary inductors: ILu2 + ILv2 + ILw2 = ILn2")
        equations.append("Node current conservation:")
        equations.append("  Node 2: ILu1 = ILu2 + ICu2")
        equations.append("  Node 4: ILv1 = ILv2 + ICv2")
        equations.append("  Node 6: ILw1 = ILw2 + ICw2")
        equations.append("  Node 11: ILn1 + ICu2 + ICv2 + ICw2 = ILn2")
        equations.append("")

        # Complete inductor voltage equations
        equations.append("Complete inductor voltage equations:")
        equations.append("Primary inductors:")
        equations.append("VLu1 = Lu1 * d(ILu1)/dt")
        equations.append("VLv1 = Lv1 * d(ILv1)/dt")
        equations.append("VLw1 = Lw1 * d(ILw1)/dt")
        equations.append("VLn1 = Ln1 * d(ILn1)/dt")
        equations.append("")
        equations.append("Secondary inductors:")
        equations.append("VLu2 = Lu2 * d(ILu2)/dt")
        equations.append("VLv2 = Lv2 * d(ILv2)/dt")
        equations.append("VLw2 = Lw2 * d(ILw2)/dt")
        equations.append("VLn2 = Ln2 * d(ILn2)/dt")
        equations.append("")

        # Using individual inductance values for all inductors
        equations.append("Using individual inductance values for all inductors:")
        equations.append("Each inductor maintains its own inductance value")
        equations.append("")

        # Complete matrix form for 8 inductors with individual values
        equations.append("Complete matrix form representation (8×8 system with individual inductances):")
        equations.append("⎡d(ILu1)/dt⎤   ⎡Lu1+Ln1  Ln1     Ln1     Ln1     0       0       0       0    ⎤⁻¹ ⎡VLu1⎤")
        equations.append("⎢d(ILv1)/dt⎥   ⎢  Ln1   Lv1+Ln1  Ln1     Ln1     0       0       0       0    ⎥   ⎢VLv1⎥")
        equations.append("⎢d(ILw1)/dt⎥   ⎢  Ln1     Ln1   Lw1+Ln1  Ln1     0       0       0       0    ⎥   ⎢VLw1⎥")
        equations.append("⎢d(ILn1)/dt⎥ = ⎢  Ln1     Ln1     Ln1   Ln1+Ln2   0       0       0       Ln2  ⎥   ⎢VLn1⎥")
        equations.append("⎢d(ILu2)/dt⎥   ⎢   0       0       0       0    Lu2+Ln2  Ln2     Ln2     Ln2  ⎥   ⎢VLu2⎥")
        equations.append("⎢d(ILv2)/dt⎥   ⎢   0       0       0       0      Ln2   Lv2+Ln2  Ln2     Ln2  ⎥   ⎢VLv2⎥")
        equations.append("⎢d(ILw2)/dt⎥   ⎢   0       0       0       0      Ln2     Ln2   Lw2+Ln2  Ln2  ⎥   ⎢VLw2⎥")
        equations.append("⎣d(ILn2)/dt⎦   ⎣   0       0       0      Ln1     Ln2     Ln2     Ln2   Ln1+Ln2⎦   ⎣VLn2⎦")
        equations.append("")

        # Primary system (3×3) for main inductors with individual inductances
        equations.append("Primary system (3×3 matrix for Lu1, Lv1, Lw1 with individual inductances):")
        equations.append("⎡d(ILu1)/dt⎤   ⎡Lu1+Ln1   Ln1     Ln1   ⎤⁻¹ ⎡Vu1 - Vn1 - Vcu2 - VRu1⎤")
        equations.append("⎢d(ILv1)/dt⎥ = ⎢  Ln1   Lv1+Ln1   Ln1   ⎥   ⎢Vv1 - Vn1 - Vcv2 - VRv1⎥")
        equations.append("⎣d(ILw1)/dt⎦   ⎣  Ln1     Ln1   Lw1+Ln1⎦   ⎣Vw1 - Vn1 - Vcw2 - VRw1⎦")
        equations.append("")

        # Secondary system (3×3) for Lu2, Lv2, Lw2
        equations.append("Secondary system (3×3 matrix for Lu2, Lv2, Lw2):")
        equations.append("Complete equations with all voltage components:")
        equations.append("Note: Secondary side should have the same structure as primary side")
        equations.append("")
        equations.append("If secondary side has independent voltage sources (Vu2, Vv2, Vw2):")
        equations.append("⎡d(ILu2)/dt⎤   ⎡Lu2+Ln2   Ln2     Ln2   ⎤⁻¹ ⎡Vu2 - Vn2 - VCu2 - VRu2⎤")
        equations.append("⎢d(ILv2)/dt⎥ = ⎢  Ln2   Lv2+Ln2   Ln2   ⎥   ⎢Vv2 - Vn2 - VCv2 - VRv2⎥")
        equations.append("⎣d(ILw2)/dt⎦   ⎣  Ln2     Ln2   Lw2+Ln2⎦   ⎣Vw2 - Vn2 - VCw2 - VRw2⎦")
        equations.append("")
        equations.append("If secondary side is coupled to primary (transformer-like behavior):")
        equations.append("⎡d(ILu2)/dt⎤   ⎡Lu2+Ln2   Ln2     Ln2   ⎤⁻¹ ⎡k*Vu1 - Vn2 - VCu2 - VRu2⎤")
        equations.append("⎢d(ILv2)/dt⎥ = ⎢  Ln2   Lv2+Ln2   Ln2   ⎥   ⎢k*Vv1 - Vn2 - VCv2 - VRv2⎥")
        equations.append("⎣d(ILw2)/dt⎦   ⎣  Ln2     Ln2   Lw2+Ln2⎦   ⎣k*Vw1 - Vn2 - VCw2 - VRw2⎦")
        equations.append("")
        equations.append("Where:")
        equations.append("  k = coupling coefficient between primary and secondary")
        equations.append("  Vu2, Vv2, Vw2 = secondary input voltages")
        equations.append("  Vn2 = secondary neutral point voltage")
        equations.append("  VCu2, VCv2, VCw2 = capacitor voltages (shared between primary and secondary)")
        equations.append("")
        equations.append("IMPORTANT NOTE:")
        equations.append("The absence of Vu2, Vv2, Vw2 in the current implementation suggests")
        equations.append("that the secondary side is not independently driven but rather")
        equations.append("magnetically coupled to the primary side through the inductors.")
        equations.append("For a complete analysis, secondary voltage sources should be defined.")
        equations.append("")
        equations.append("Current implementation uses coupled approach:")
        equations.append("⎡d(ILu2)/dt⎤   ⎡Lu2+Ln2   Ln2     Ln2   ⎤⁻¹ ⎡Vu1 - Vn1 - VCu2 - VRu1 - VRu2 - VLn2⎤")
        equations.append("⎢d(ILv2)/dt⎥ = ⎢  Ln2   Lv2+Ln2   Ln2   ⎥   ⎢Vv1 - Vn1 - VCv2 - VRv1 - VRv2 - VLn2⎥")
        equations.append("⎣d(ILw2)/dt⎦   ⎣  Ln2     Ln2   Lw2+Ln2⎦   ⎣Vw1 - Vn1 - VCw2 - VRw1 - VRw2 - VLn2⎦")
        equations.append("")
        equations.append("Alternative simplified form (using node voltages):")
        equations.append("⎡d(ILu2)/dt⎤   ⎡Lu2+Ln2   Ln2     Ln2   ⎤⁻¹ ⎡Vn2 - Vn12 - VRu2⎤")
        equations.append("⎢d(ILv2)/dt⎥ = ⎢  Ln2   Lv2+Ln2   Ln2   ⎥   ⎢Vn4 - Vn13 - VRv2⎥")
        equations.append("⎣d(ILw2)/dt⎦   ⎣  Ln2     Ln2   Lw2+Ln2⎦   ⎣Vn6 - Vn14 - VRw2⎦")
        equations.append("Note: This simplified form omits the coupling terms for clarity")
        equations.append("")

        # Detailed explanation of voltage differences
        equations.append("EXPLANATION: Why Primary and Secondary systems have different voltage terms:")
        equations.append("")
        equations.append("Primary system (Lu1, Lv1, Lw1):")
        equations.append("  - Connected directly to external voltage sources (Vu1, Vv1, Vw1)")
        equations.append("  - Includes input resistors (Ru1, Rv1, Rw1)")
        equations.append("  - Includes capacitors (Cu2, Cv2, Cw2) to ground")
        equations.append("  - Includes neutral inductor (Ln1)")
        equations.append("  - Full voltage equation: Vu1 - VRu1 - VLu1 - VCu2 - VLn1 = 0")
        equations.append("")
        equations.append("Secondary system (Lu2, Lv2, Lw2):")
        equations.append("  - Connected to intermediate nodes (2, 4, 6) not external sources")
        equations.append("  - Includes output resistors (Ru2, Rv2, Rw2)")
        equations.append("  - Shares the same capacitors and neutral inductors as primary")
        equations.append("  - Voltage sources are implicit through node voltage relationships")
        equations.append("  - Simplified voltage equation: Vnode - Voutput - VRsecondary = 0")
        equations.append("")
        equations.append("The apparent 'missing' voltage terms (Vu1, VCu2) in secondary system are:")
        equations.append("  1. Vu1, Vv1, Vw1: External sources only affect primary system directly")
        equations.append("  2. VCu2, VCv2, VCw2: Shared between systems, accounted in node voltages")
        equations.append("  3. VLn1, VLn2: Different neutral inductors for each system")
        equations.append("")

        # Neutral inductors with individual values
        equations.append("Neutral inductor equations:")
        equations.append("d(ILn1)/dt = (VLn1) / Ln1 = (Vn10 - Vn11) / Ln1")
        equations.append("d(ILn2)/dt = (VLn2) / Ln2 = (Vn11 - Vn15) / Ln2")
        equations.append("")

        # Add common analysis sections
        equations.extend(self._add_common_analysis())

        return equations

    def _add_common_analysis(self) -> List[str]:
        """Add common analysis sections (capacitor relationships, etc.)"""
        equations = []

        # Capacitor current relationships based on actual circuit topology
        equations.append("Capacitor current relationships (KCL):")
        capacitor_relationships = self._generate_capacitor_current_relationships()
        for relationship in capacitor_relationships:
            equations.append(relationship)
        equations.append("")

        # Capacitor voltage equations
        equations.append("Capacitor voltage equations:")
        equations.append("Vcu2 = (1/Cu2) * ∫ICu2 dt")
        equations.append("Vcv2 = (1/Cv2) * ∫ICv2 dt")
        equations.append("Vcw2 = (1/Cw2) * ∫ICw2 dt")
        equations.append("")

        return equations

    def _generate_capacitor_current_relationships(self) -> List[str]:
        """Generate capacitor current relationships based on actual circuit topology"""
        relationships = []

        # Get all capacitors
        capacitors = self.parser.get_capacitors()

        for cap_name, cap_component in capacitors.items():
            # Find the non-ground node where the capacitor is connected
            non_ground_node = None
            if cap_component.node1 != 11:  # Assuming 11 is ground
                non_ground_node = cap_component.node1
            elif cap_component.node2 != 11:
                non_ground_node = cap_component.node2

            if non_ground_node is None:
                continue  # Skip if both nodes are ground

            # Get all components connected to this non-ground node
            connected_components = self.parser.get_connected_components(non_ground_node)

            # Build KCL equation for this node and extract capacitor current relationship
            incoming_currents = []
            outgoing_currents = []

            for comp in connected_components:
                current_name = f"I{comp.name.lower()}"

                if comp.name == cap_name:
                    # This is the capacitor itself - determine its direction
                    if comp.node1 == non_ground_node:
                        outgoing_currents.append(current_name)
                    else:
                        incoming_currents.append(current_name)
                else:
                    # Other components
                    if comp.node1 == non_ground_node:
                        outgoing_currents.append(current_name)
                    else:
                        incoming_currents.append(current_name)

            # Generate relationship: capacitor current = sum of incoming - sum of other outgoing
            cap_current = f"I{cap_name}"
            other_outgoing = [curr for curr in outgoing_currents if curr != f"I{cap_name.lower()}"]

            if len(incoming_currents) == 1 and len(other_outgoing) == 0:
                # Simple case: I_cap = I_incoming
                relationships.append(f"{cap_current} = {incoming_currents[0]}")
            elif len(incoming_currents) == 1 and len(other_outgoing) == 1:
                # Two-component case: I_cap = I_incoming - I_other_outgoing
                relationships.append(f"{cap_current} = {incoming_currents[0]} - {other_outgoing[0]}")
            else:
                # More complex case
                incoming_str = " + ".join(incoming_currents) if incoming_currents else "0"
                outgoing_str = " + ".join(other_outgoing) if other_outgoing else "0"
                if outgoing_str != "0":
                    relationships.append(f"{cap_current} = {incoming_str} - ({outgoing_str})")
                else:
                    relationships.append(f"{cap_current} = {incoming_str}")

        return relationships

if __name__ == "__main__":
    # Test the advanced equation generator
    from advanced_circuit_parser import AdvancedCircuitParser

    parser = AdvancedCircuitParser()
    parser.parse_file("input/ACF2.cct")

    generator = AdvancedEquationGenerator(parser)
    equations = generator.generate_all_equations()

    print(generator.generate_text_equations())

    # Generate ACF2 special equations
    special_eqs = generator.generate_acf2_special_equations()
    if special_eqs:
        print("\n".join(special_eqs))
