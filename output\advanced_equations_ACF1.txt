=== ADVANCED CIRCUIT EQUATIONS ===
Source file: input/ACF1.cct

Capacitor Cu1 (C = Cu1):
  Nodes: 1 - 4
  Voltage: V_cu1 = Vn1 - Vn4
  Current (continuous): I_cu1 = Cu1 * d(V_cu1)/dt
  Current (discrete): I_cu1 = Cu1 * ((Vn1 - Vn4) - V_cu1_old) / dt
  Integration: V_cu1 = (1/Cu1) * ∫I_cu1 dt

Capacitor Cv1 (C = Cv1):
  Nodes: 2 - 4
  Voltage: V_cv1 = Vn2 - Vn4
  Current (continuous): I_cv1 = Cv1 * d(V_cv1)/dt
  Current (discrete): I_cv1 = Cv1 * ((Vn2 - Vn4) - V_cv1_old) / dt
  Integration: V_cv1 = (1/Cv1) * ∫I_cv1 dt

Capacitor Cw1 (C = Cw1):
  Nodes: 3 - 4
  Voltage: V_cw1 = Vn3 - Vn4
  Current (continuous): I_cw1 = Cw1 * d(V_cw1)/dt
  Current (discrete): I_cw1 = Cw1 * ((Vn3 - Vn4) - V_cw1_old) / dt
  Integration: V_cw1 = (1/Cw1) * ∫I_cw1 dt

=== KIRCHHOFF'S CURRENT LAW (KCL) ===
Complete nodal analysis with component equations:
Each node shows:
  - All connected components and their types
  - Voltage equations for each component
  - Current-voltage relationships (Ohm's law, L/C dynamics)
  - Energy equations for reactive components
  - Current direction and KCL equation

Node 4 (GROUND) (Vn4):
  Connected components and their equations:
    Cu1 (C): 1→4
      Voltage: V_cu1 = Vn1 - Vn4
      Current-voltage relation: I_cu1 = Cu1 * d(V_cu1)/dt
      Voltage (continuous): d(V_cu1)/dt = I_cu1 / Cu1
      Voltage (discrete): V_cu1 = V_cu1_old + (I_cu1 * dt) / Cu1
      Energy: E_cu1 = (1/2) * Cu1 * V_cu1²
      Current direction: incoming (I_cu1)

    Cv1 (C): 2→4
      Voltage: V_cv1 = Vn2 - Vn4
      Current-voltage relation: I_cv1 = Cv1 * d(V_cv1)/dt
      Voltage (continuous): d(V_cv1)/dt = I_cv1 / Cv1
      Voltage (discrete): V_cv1 = V_cv1_old + (I_cv1 * dt) / Cv1
      Energy: E_cv1 = (1/2) * Cv1 * V_cv1²
      Current direction: incoming (I_cv1)

    Cw1 (C): 3→4
      Voltage: V_cw1 = Vn3 - Vn4
      Current-voltage relation: I_cw1 = Cw1 * d(V_cw1)/dt
      Voltage (continuous): d(V_cw1)/dt = I_cw1 / Cw1
      Voltage (discrete): V_cw1 = V_cw1_old + (I_cw1 * dt) / Cw1
      Energy: E_cw1 = (1/2) * Cw1 * V_cw1²
      Current direction: incoming (I_cw1)

  Kirchhoff's Current Law (KCL):
    +I_cu1 +I_cv1 +I_cw1 = 0
    Physical meaning: ∑I_incoming = ∑I_outgoing

